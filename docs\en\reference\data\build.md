---
description: Explore the Ultralytics YOLO v3 data build procedures, including the InfiniteD<PERSON><PERSON>oa<PERSON>, seed_worker, build_dataloader, and load_inference_source.
keywords: Ultralytics, YOLO v3, Data build, DataLoader, InfiniteDataLoader, seed_worker, build_dataloader, load_inference_source
---

# Reference for `ultralytics/data/build.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/build.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/build.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/data/build.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.data.build.InfiniteDataLoader

<br><br>

## ::: ultralytics.data.build._RepeatSampler

<br><br>

## ::: ultralytics.data.build.seed_worker

<br><br>

## ::: ultralytics.data.build.build_yolo_dataset

<br><br>

## ::: ultralytics.data.build.build_dataloader

<br><br>

## ::: ultralytics.data.build.check_source

<br><br>

## ::: ultralytics.data.build.load_inference_source

<br><br>

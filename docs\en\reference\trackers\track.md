---
description: Explore Ultralytics documentation on prediction function starters & register trackers. Understand our code & its applications better.
keywords: Ultralytics, YOLO, on predict start, register tracker, prediction functions, documentation
---

# Reference for `ultralytics/trackers/track.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/track.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/track.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/trackers/track.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.trackers.track.on_predict_start

<br><br>

## ::: ultralytics.trackers.track.on_predict_postprocess_end

<br><br>

## ::: ultralytics.trackers.track.register_tracker

<br><br>

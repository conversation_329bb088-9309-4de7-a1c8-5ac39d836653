---
description: Explore KalmanFilterXYAH, a key component of Ultralytics trackers. Understand its utilities and learn to leverage it in your own projects.
keywords: Ultralytics, KalmanFilterXYAH, tracker, documentation, guide
---

# Reference for `ultralytics/trackers/utils/kalman_filter.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/utils/kalman_filter.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/utils/kalman_filter.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/trackers/utils/kalman_filter.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.trackers.utils.kalman_filter.KalmanFilterXYAH

<br><br>

## ::: ultralytics.trackers.utils.kalman_filter.KalmanFilterXYWH

<br><br>

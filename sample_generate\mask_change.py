#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量将 LabelMe 风格的 JSON 标注转换成缺陷掩码 PNG。
只提取异常（非 'good'）的多边形区域，填充为白色，其余区域为黑色。
"""

import os
import json
import logging
from multiprocessing import Pool, cpu_count

import cv2
import numpy as np

class MaskConverter:
    """
    将单个 JSON 标注文件转换为二值掩码图（0/255）。
    """
    def __init__(self, annotations_dir: str, output_dir: str,
                 resize: tuple = None):
        """
        :param annotations_dir: JSON 标注根目录
        :param output_dir:      掩码输出根目录
        :param resize:          可选，(width, height) 强制缩放
        """
        self.annotations_dir = annotations_dir
        self.output_dir = output_dir
        self.resize = resize

    def convert_all(self, num_workers: int = 1):
        """
        遍历 annotations_dir 下所有 JSON，并行或串行转换。
        :param num_workers: 使用的进程数
        """
        # 收集所有 JSON 文件路径
        json_paths = []
        for root, _, files in os.walk(self.annotations_dir):
            for fname in files:
                if fname.lower().endswith('.json'):
                    json_paths.append(os.path.join(root, fname))
        logging.info(f"发现 {len(json_paths)} 个 JSON 标注文件，开始转换...")

        # 并行或串行执行
        if num_workers > 1:
            num_workers = min(num_workers, cpu_count())
            with Pool(num_workers) as pool:
                pool.map(self._process_single, json_paths)
        else:
            for path in json_paths:
                self._process_single(path)

        logging.info("批量转换完成。")

    def _process_single(self, json_path: str):
        """
        处理单个 JSON，生成对应的 _mask.png。
        """
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            logging.error(f"读取 JSON 失败: {json_path}，跳过。错误：{e}")
            return

        # 获取原图尺寸
        img_w = data.get('imageWidth')
        img_h = data.get('imageHeight')
        if img_w is None or img_h is None:
            logging.warning(f"缺少 imageWidth/Height 信息: {json_path}，跳过。")
            return

        # 确定输出尺寸
        out_w, out_h = (self.resize if self.resize else (img_w, img_h))

        # 创建全 0 掩码
        mask = np.zeros((out_h, out_w), dtype=np.uint8)

        # 遍历所有 shapes，多边形填充
        for shape in data.get('shapes', []):
            label = shape.get('label', '').strip().lower()
            # 仅填充非 'good' 标签的区域
            if label == 'good':
                continue
            points = np.array(shape.get('points', []), dtype=np.int32)
            if points.size == 0:
                continue
            # 如果需要缩放坐标
            if self.resize:
                scale_x = out_w / img_w
                scale_y = out_h / img_h
                points = np.round(points * np.array([scale_x, scale_y])).astype(np.int32)
            cv2.fillPoly(mask, [points], color=255)

        # 构造输出路径，保持与输入同样的相对结构
        rel_path = os.path.relpath(json_path, self.annotations_dir)
        save_path = os.path.join(
            self.output_dir,
            rel_path.replace('.json', '_mask.png')
        )
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # 保存掩码
        success = cv2.imwrite(save_path, mask)
        if not success:
            logging.error(f"保存掩码失败: {save_path}")
        else:
            logging.debug(f"已保存掩码: {save_path}")

def main():
    # ========== 在脚本内直接配置参数（请根据实际情况修改） ==========
    ANNOTATIONS_DIR = r"D:\YOLO\1"    # 标注 JSON 根目录
    OUTPUT_DIR      = r"D:\YOLO\1"   # 掩码 PNG 输出根目录
    WORKERS         = 1                        # 并行进程数，设为 1 则串行执行
    RESIZE          = None                      # 可选，(width, height)，例如 (1024, 1024) 或 None
    # =============================================================

    # 日志配置
    logging.basicConfig(
        level=logging.INFO,
        format="[%(asctime)s] %(levelname)s: %(message)s",
        datefmt="%H:%M:%S"
    )

    # 创建并运行转换器
    converter = MaskConverter(
        annotations_dir=ANNOTATIONS_DIR,
        output_dir=OUTPUT_DIR,
        resize=RESIZE
    )
    converter.convert_all(num_workers=WORKERS)

if __name__ == "__main__":
    main()
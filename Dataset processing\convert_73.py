import os
import cv2
import numpy as np
import yaml
import shutil
from pathlib import Path
import random
from sklearn.model_selection import train_test_split

class MVTecADConverter:
    def __init__(self, input_path, output_path, train_ratio=0.7, val_ratio=0.2, test_ratio=0.1):
        """
        MVTec AD数据集转换器 (原始类别版本)
        
        Args:
            input_path: 原始MVTec AD数据集路径
            output_path: 输出YOLO格式数据集路径
            train_ratio: 训练集比例
            val_ratio: 验证集比例  
            test_ratio: 测试集比例
        """
        self.input_path = Path(input_path)
        self.output_path = Path(output_path)
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = test_ratio
        
        # 确保比例之和为1
        total_ratio = train_ratio + val_ratio + test_ratio
        if abs(total_ratio - 1.0) > 0.001:
            raise ValueError(f"比例之和必须为1.0，当前为{total_ratio}")

        # 将在处理时动态发现类别
        self.class_names = []
        self.class_to_id = {}
        
    def create_output_structure(self):
        """创建输出目录结构"""
        dirs_to_create = [
            'train/images', 'train/labels',
            'val/images', 'val/labels', 
            'test/images', 'test/labels'
        ]
        
        for dir_path in dirs_to_create:
            (self.output_path / dir_path).mkdir(parents=True, exist_ok=True)
            
    def mask_to_bbox(self, mask_path):
        """
        将掩码转换为YOLO格式的边界框
        
        Args:
            mask_path: 掩码文件路径
            
        Returns:
            list: YOLO格式的边界框列表 [class_id, x_center, y_center, width, height]
        """
        try:
            # 读取掩码图像
            mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
            if mask is None:
                print(f"警告: 无法读取掩码文件 {mask_path}")
                return []
            
            h, w = mask.shape
            
            # 找到所有非零像素(缺陷区域)
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            bboxes = []
            for contour in contours:
                # 计算边界框
                x, y, box_w, box_h = cv2.boundingRect(contour)
                
                # 过滤太小的区域
                if box_w < 5 or box_h < 5:
                    continue
                
                # 转换为YOLO格式 (归一化坐标)
                x_center = (x + box_w / 2) / w
                y_center = (y + box_h / 2) / h
                norm_width = box_w / w
                norm_height = box_h / h
                
                # 确保坐标在[0,1]范围内
                x_center = max(0, min(1, x_center))
                y_center = max(0, min(1, y_center))
                norm_width = max(0, min(1, norm_width))
                norm_height = max(0, min(1, norm_height))
                
                bboxes.append([x_center, y_center, norm_width, norm_height])
                
            return bboxes
            
        except Exception as e:
            print(f"处理掩码文件 {mask_path} 时出错: {e}")
            return []

    def discover_classes(self):
        """遍历数据集以发现所有原始类别"""
        print("开始扫描数据集以发现所有类别...")
        
        discovered_classes = set()
        for product_dir in self.input_path.iterdir():
            if not product_dir.is_dir() or product_dir.name in ['license.txt', 'readme.txt']:
                continue

            product_name = product_dir.name
            test_dir = product_dir / 'test'

            if not test_dir.is_dir():
                continue

            for defect_dir in test_dir.iterdir():
                if defect_dir.is_dir() and defect_dir.name != 'good':
                    defect_type = defect_dir.name
                    class_name = f"{product_name}_{defect_type}"
                    discovered_classes.add(class_name)
        
        self.class_names = sorted(list(discovered_classes))
        self.class_to_id = {name: i for i, name in enumerate(self.class_names)}
        
        print(f"发现 {len(self.class_names)} 个独立类别。")
        if not self.class_names:
            raise ValueError("在数据集中未发现任何缺陷类别，请检查数据集结构。")
    
    def process_dataset(self):
        """处理整个数据集"""
        print("开始处理MVTec AD数据集 (原始类别模式)...")
        
        # 1. 动态发现所有类别
        self.discover_classes()
        
        # 2. 创建输出目录结构
        self.create_output_structure()
        
        # 3. 遍历数据集，提取样本
        all_samples = []
        total_images = 0
        total_defects = 0
        class_counts = {i: 0 for i in range(len(self.class_names))}
        
        for product_dir in self.input_path.iterdir():
            if not product_dir.is_dir() or product_dir.name in ['license.txt', 'readme.txt']:
                continue

            product_name = product_dir.name
            print(f"\n处理产品: {product_name}")

            test_dir = product_dir / 'test'
            ground_truth_dir = product_dir / 'ground_truth'

            if not test_dir.is_dir():
                print(f"  警告: 在 '{product_name}' 中未找到 'test' 文件夹，跳过。")
                continue

            for defect_dir in test_dir.iterdir():
                if not defect_dir.is_dir() or defect_dir.name == 'good':
                    continue

                defect_type = defect_dir.name
                print(f"  处理缺陷类型: {defect_type}")
                
                class_name = f"{product_name}_{defect_type}"
                class_id = self.class_to_id[class_name]

                mask_dir = ground_truth_dir / defect_type
                if not mask_dir.is_dir():
                    print(f"    警告: 找不到对应的掩码文件夹: {mask_dir}")
                    continue
                
                product_defect_images = 0
                for img_file in defect_dir.iterdir():
                    if not img_file.is_file() or not img_file.name.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp')):
                        continue
                    
                    if '_mask' in img_file.name.lower():
                        continue

                    mask_file = mask_dir / f"{img_file.stem}_mask.png"

                    if not mask_file.exists():
                        print(f"    警告: 找不到图像 {img_file.name} 对应的掩码文件: {mask_file}")
                        continue

                    bboxes = self.mask_to_bbox(mask_file)
                    if not bboxes:
                        continue

                    sample_info = {
                        'image_path': img_file,
                        'class_id': class_id,
                        'bboxes': bboxes
                    }
                    all_samples.append(sample_info)

                    total_images += 1
                    product_defect_images += 1
                    total_defects += len(bboxes)
                    class_counts[class_id] += len(bboxes)

                if product_defect_images > 0:
                    print(f"    处理完成: {product_defect_images} 张图像")

        print(f"\n数据集处理完成:")
        print(f"总图像数: {total_images}")
        print(f"总缺陷数: {total_defects}")
        print(f"类别分布: {class_counts}")

        if not all_samples:
            print("\n警告: 未找到任何有效的缺陷样本，无法进行下一步。")
            return 0, 0, {}

        # 4. 分割并保存数据集
        self.split_and_save_dataset(all_samples)
        
        # 5. 生成配置文件
        self.generate_yaml_config()
        
        return total_images, total_defects, class_counts
    
    def split_and_save_dataset(self, all_samples):
        """分割数据集并保存"""
        print(f"\n开始分割数据集...")
        print(f"分割比例 - 训练集: {self.train_ratio}, 验证集: {self.val_ratio}, 测试集: {self.test_ratio}")
        
        random.shuffle(all_samples)
        
        n_samples = len(all_samples)
        train_end = int(n_samples * self.train_ratio)
        val_end = train_end + int(n_samples * self.val_ratio)
        
        train_samples = all_samples[:train_end]
        val_samples = all_samples[train_end:val_end]
        test_samples = all_samples[val_end:]
        
        print(f"训练集: {len(train_samples)} 张图像")
        print(f"验证集: {len(val_samples)} 张图像")
        print(f"测试集: {len(test_samples)} 张图像")
        
        self.save_split('train', train_samples)
        self.save_split('val', val_samples)
        self.save_split('test', test_samples)
    
    def save_split(self, split_name, samples):
        """保存数据集分割"""
        print(f"\n保存{split_name}数据集...")
        
        # 为每个split的图像从0开始编号
        for i, sample in enumerate(samples):
            new_name = f"{i:06d}"
            
            img_dst = self.output_path / split_name / 'images' / f"{new_name}.png"
            try:
                image = cv2.imread(str(sample['image_path']))
                if image is None:
                    raise IOError(f"无法读取图像文件: {sample['image_path']}")
                cv2.imwrite(str(img_dst), image)
            except Exception as e:
                print(f"警告: 使用OpenCV转换图像失败 ({e}), 将直接复制文件: {sample['image_path']}")
                shutil.copy2(sample['image_path'], img_dst)

            label_dst = self.output_path / split_name / 'labels' / f"{new_name}.txt"
            with open(label_dst, 'w') as f:
                for bbox in sample['bboxes']:
                    x_center, y_center, width, height = bbox
                    f.write(f"{sample['class_id']} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")
        
        print(f"{split_name}数据集保存完成: {len(samples)} 张图像")
    
    def generate_yaml_config(self):
        """生成YOLO配置文件"""
        config = {
            'path': self.output_path.absolute().as_posix(),
            'train': 'train/images',
            'val': 'val/images',
            'test': 'test/images',
            'nc': len(self.class_names),
            'names': [f'{i}: {name}' for i, name in enumerate(self.class_names)]
        }
        
        yaml_path = self.output_path / 'dataset.yaml'
        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2, sort_keys=False)
        
        print(f"\n配置文件已生成: {yaml_path}")

def main():
    """主函数"""
    print("=== MVTec AD数据集转换工具 (原始类别版本) ===\n")
    
    # ===== 配置参数区域 =====
    # 请在这里修改输入和输出路径
    INPUT_PATH = r"D:\YOLO\datasets\MVTec_AD"  # 原始MVTec AD数据集路径
    OUTPUT_PATH = r"D:\YOLO\datasets\MVTec_AD_YOLO"  # 建议为原始类别版本使用新的输出目录
    
    # 数据集分割比例 (确保三者之和为1.0)
    TRAIN_RATIO = 0.8  # 训练集比例
    VAL_RATIO = 0.1     # 验证集比例  
    TEST_RATIO = 0.1    # 测试集比例
    # ========================
    
    # 验证路径
    input_path = Path(INPUT_PATH)
    if not input_path.exists():
        print(f"错误: 输入路径不存在 - {INPUT_PATH}")
        print("请确认MVTec AD数据集路径是否正确")
        return
    
    output_path = Path(OUTPUT_PATH)
    print(f"输入路径: {INPUT_PATH}")
    print(f"输出路径: {OUTPUT_PATH}")
    print(f"数据集分割比例: 训练集={TRAIN_RATIO}, 验证集={VAL_RATIO}, 测试集={TEST_RATIO}")
    
    # 确认执行
    response = input("\n是否开始转换? (y/N): ")
    if response.lower() != 'y':
        print("转换已取消")
        return
    
    try:
        # 创建转换器并执行转换
        converter = MVTecADConverter(
            input_path=INPUT_PATH,
            output_path=OUTPUT_PATH,
            train_ratio=TRAIN_RATIO,
            val_ratio=VAL_RATIO,
            test_ratio=TEST_RATIO
        )
        
        total_images, total_defects, class_counts = converter.process_dataset()
        
        print("\n" + "="*50)
        print("转换完成!")
        print(f"总共处理图像: {total_images} 张")
        print(f"总共检测缺陷: {total_defects} 个")
        print(f"总共发现类别: {len(converter.class_names)} 个")
        print(f"输出目录: {OUTPUT_PATH}")
        print("\n各类别统计:")
        for class_id, count in class_counts.items():
            if count > 0:
                print(f"  {class_id}: {converter.class_names[class_id]} - {count} 个缺陷")
        print("="*50)
        
    except Exception as e:
        print(f"\n转换过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 
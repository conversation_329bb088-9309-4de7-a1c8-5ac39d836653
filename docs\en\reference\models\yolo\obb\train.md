---
description: Master the Ultralytics YOLO OBB Trainer: A specialized tool for training YOLO models using Oriented Bounding Boxes. Features detailed usage, model initialization, and training processes.
keywords: Ultralytics, YOLO OBB Trainer, Oriented Bounding Box, OBB model training, YOLO model training, computer vision, deep learning, machine learning, YOLO object detection, model initialization, YOLO training process
---

# Reference for `ultralytics/models/yolo/obb/train.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/obb/train.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/obb/train.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/obb/train.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.yolo.obb.train.OBBTrainer

<br><br>

---
description: Explore Ultralytics documentation for check_train_batch_size utility in the autobatch module. Understand how it could improve your machine learning process.
keywords: Ultralytics, check_train_batch_size, autobatch, utility, machine learning, documentation
---

# Reference for `ultralytics/utils/autobatch.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/autobatch.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/autobatch.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/autobatch.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.autobatch.check_train_batch_size

<br><br>

## ::: ultralytics.utils.autobatch.autobatch

<br><br>

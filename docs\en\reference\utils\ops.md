---
description: Explore detailed documentation for Ultralytics utility operations. Learn about methods like segment2box, make_divisible, clip_boxes, and many more.
keywords: Ultralytics YOLO, Utility Operations, segment2box, make_divisible, clip_boxes, scale_image, xywh2xyxy, xyxy2xywhn, xywh2ltwh, ltwh2xywh, segments2boxes, crop_mask, process_mask, scale_masks, masks2segments
---

# Reference for `ultralytics/utils/ops.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/ops.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/ops.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/ops.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.ops.Profile

<br><br>

## ::: ultralytics.utils.ops.segment2box

<br><br>

## ::: ultralytics.utils.ops.scale_boxes

<br><br>

## ::: ultralytics.utils.ops.make_divisible

<br><br>

## ::: ultralytics.utils.ops.nms_rotated

<br><br>

## ::: ultralytics.utils.ops.non_max_suppression

<br><br>

## ::: ultralytics.utils.ops.clip_boxes

<br><br>

## ::: ultralytics.utils.ops.clip_coords

<br><br>

## ::: ultralytics.utils.ops.scale_image

<br><br>

## ::: ultralytics.utils.ops.xyxy2xywh

<br><br>

## ::: ultralytics.utils.ops.xywh2xyxy

<br><br>

## ::: ultralytics.utils.ops.xywhn2xyxy

<br><br>

## ::: ultralytics.utils.ops.xyxy2xywhn

<br><br>

## ::: ultralytics.utils.ops.xywh2ltwh

<br><br>

## ::: ultralytics.utils.ops.xyxy2ltwh

<br><br>

## ::: ultralytics.utils.ops.ltwh2xywh

<br><br>

## ::: ultralytics.utils.ops.xyxyxyxy2xywhr

<br><br>

## ::: ultralytics.utils.ops.xywhr2xyxyxyxy

<br><br>

## ::: ultralytics.utils.ops.ltwh2xyxy

<br><br>

## ::: ultralytics.utils.ops.segments2boxes

<br><br>

## ::: ultralytics.utils.ops.resample_segments

<br><br>

## ::: ultralytics.utils.ops.crop_mask

<br><br>

## ::: ultralytics.utils.ops.process_mask_upsample

<br><br>

## ::: ultralytics.utils.ops.process_mask

<br><br>

## ::: ultralytics.utils.ops.process_mask_native

<br><br>

## ::: ultralytics.utils.ops.scale_masks

<br><br>

## ::: ultralytics.utils.ops.scale_coords

<br><br>

## ::: ultralytics.utils.ops.regularize_rboxes

<br><br>

## ::: ultralytics.utils.ops.masks2segments

<br><br>

## ::: ultralytics.utils.ops.convert_torch2numpy_batch

<br><br>

## ::: ultralytics.utils.ops.clean_str

<br><br>

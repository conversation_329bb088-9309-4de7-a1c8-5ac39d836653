---
description: Explore the detailed guide on using the Ultralytics YOLO Engine Model. Learn better ways to implement, train and evaluate YOLO models.
keywords: Ultralytics, YOLO, engine model, documentation, guide, implementation, training, evaluation
---

# Reference for `ultralytics/engine/model.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/engine/model.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/engine/model.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/engine/model.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.engine.model.Model

<br><br>

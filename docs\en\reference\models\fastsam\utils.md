---
description: Learn how to adjust bounding boxes to image borders in Ultralytics models using the bbox_iou utility. Enhance your object detection performance.
keywords: Ultralytics, bounding boxes, Bboxes, image borders, object detection, bbox_iou, model utilities
---

# Reference for `ultralytics/models/fastsam/utils.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/fastsam/utils.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/fastsam/utils.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/fastsam/utils.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.fastsam.utils.adjust_bboxes_to_image_border

<br><br>

## ::: ultralytics.models.fastsam.utils.bbox_iou

<br><br>

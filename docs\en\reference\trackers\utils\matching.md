---
description: Explore in-depth guidance for using Ultralytics trackers utils matching, including merge_matches, linear_assignment, iou_distance, embedding_distance, fuse_motion, and fuse_score.
keywords: Ultralytics, Trackers Utils, Matching, merge_matches, linear_assignment, iou_distance, embedding_distance, fuse_motion, fuse_score, documentation
---

# Reference for `ultralytics/trackers/utils/matching.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/utils/matching.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/utils/matching.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/trackers/utils/matching.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.trackers.utils.matching.linear_assignment

<br><br>

## ::: ultralytics.trackers.utils.matching.iou_distance

<br><br>

## ::: ultralytics.trackers.utils.matching.embedding_distance

<br><br>

## ::: ultralytics.trackers.utils.matching.fuse_score

<br><br>

---
description: Explore exhaustive details about Ultralytics callbacks in Neptune, with specifics about scalar logging, routine start, and more.
keywords: Ultralytics, Neptune callbacks, on_train_epoch_end, on_val_end, _log_plot, _log_images, on_pretrain_routine_start, on_fit_epoch_end, on_train_end
---

# Reference for `ultralytics/utils/callbacks/neptune.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/neptune.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/neptune.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/callbacks/neptune.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.callbacks.neptune._log_scalars

<br><br>

## ::: ultralytics.utils.callbacks.neptune._log_images

<br><br>

## ::: ultralytics.utils.callbacks.neptune._log_plot

<br><br>

## ::: ultralytics.utils.callbacks.neptune.on_pretrain_routine_start

<br><br>

## ::: ultralytics.utils.callbacks.neptune.on_train_epoch_end

<br><br>

## ::: ultralytics.utils.callbacks.neptune.on_fit_epoch_end

<br><br>

## ::: ultralytics.utils.callbacks.neptune.on_val_end

<br><br>

## ::: ultralytics.utils.callbacks.neptune.on_train_end

<br><br>

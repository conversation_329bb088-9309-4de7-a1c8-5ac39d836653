---
description: Discover how to use Ultralytics utility functions for file-related operations including incrementing paths, finding file age, checking file size and creating directories.
keywords: Ultralytics, utility functions, file operations, working directory, file age, file size, create directories
---

# Reference for `ultralytics/utils/files.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/files.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/files.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/files.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.files.WorkingDirectory

<br><br>

## ::: ultralytics.utils.files.spaces_in_path

<br><br>

## ::: ultralytics.utils.files.increment_path

<br><br>

## ::: ultralytics.utils.files.file_age

<br><br>

## ::: ultralytics.utils.files.file_date

<br><br>

## ::: ultralytics.utils.files.file_size

<br><br>

## ::: ultralytics.utils.files.get_latest_run

<br><br>

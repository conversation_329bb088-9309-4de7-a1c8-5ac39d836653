---
description: Understand routines at the end of pre-training and training in Ultralytics. Elevate your MLflow callbacks expertise.
keywords: Ultralytics, ML<PERSON>, Callbacks, on_pretrain_routine_end, on_train_end, Machine Learning, Training
---

# Reference for `ultralytics/utils/callbacks/mlflow.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/mlflow.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/mlflow.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/callbacks/mlflow.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.callbacks.mlflow.on_pretrain_routine_end

<br><br>

## ::: ultralytics.utils.callbacks.mlflow.on_train_epoch_end

<br><br>

## ::: ultralytics.utils.callbacks.mlflow.on_fit_epoch_end

<br><br>

## ::: ultralytics.utils.callbacks.mlflow.on_train_end

<br><br>

---
description: Master the use of Ultralytics BOTrack, a key component of the powerful Ultralytics tracking system. Learn to integrate and use BOTSORT in your projects.
keywords: Ultralytics, BOTSORT, BOTrack, tracking system, official documentation, machine learning, AI tracking
---

# Reference for `ultralytics/trackers/bot_sort.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/bot_sort.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/bot_sort.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/trackers/bot_sort.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.trackers.bot_sort.BOTrack

<br><br>

## ::: ultralytics.trackers.bot_sort.BOTSORT

<br><br>

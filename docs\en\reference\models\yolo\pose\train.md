---
description: Explore Ultralytics PoseTrainer for YOLO models. Get a step-by-step guide on how to train on custom pose data for more accurate AI modeling.
keywords: Ultralytics, YOLO, PoseTrainer, pose training, AI modeling, custom data training
---

# Reference for `ultralytics/models/yolo/pose/train.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/pose/train.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/pose/train.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/pose/train.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.yolo.pose.train.PoseTrainer

<br><br>

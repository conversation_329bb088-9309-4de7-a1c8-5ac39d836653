---
description: Explore Ultralytics docs for various Events, including "request_with_credentials" and "requests_with_progress". Also, understand the use of the "smart_request".
keywords: Ultralytics, Events, request_with_credentials, smart_request, Ultralytics hub utils, requests_with_progress
---

# Reference for `ultralytics/hub/utils.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/hub/utils.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/hub/utils.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/hub/utils.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.hub.utils.Events

<br><br>

## ::: ultralytics.hub.utils.request_with_credentials

<br><br>

## ::: ultralytics.hub.utils.requests_with_progress

<br><br>

## ::: ultralytics.hub.utils.smart_request

<br><br>

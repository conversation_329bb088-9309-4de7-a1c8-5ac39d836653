# Ultralytics YOLO 🚀, AGPL-3.0 license
# Pre-commit hooks. For more information see https://github.com/pre-commit/pre-commit-hooks/blob/main/README.md
# Optionally remove from local hooks with 'rm .git/hooks/pre-commit'

# Define bot property if installed via https://github.com/marketplace/pre-commit-ci
ci:
  autofix_prs: true
  autoupdate_commit_msg: "[pre-commit.ci] pre-commit suggestions"
  autoupdate_schedule: monthly
  submodules: true

# Exclude directories (optional)
# exclude: 'docs/'

# Define repos to run
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: end-of-file-fixer
      - id: trailing-whitespace
      - id: check-case-conflict
      # - id: check-yaml
      - id: check-docstring-first
      - id: detect-private-key

  - repo: https://github.com/asottile/pyupgrade
    rev: v3.15.0
    hooks:
      - id: pyupgrade
        name: Upgrade code

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.1.11
    hooks:
      - id: ruff
        args: [--fix]

  - repo: https://github.com/executablebooks/mdformat
    rev: 0.7.17
    hooks:
      - id: mdformat
        name: MD formatting
        additional_dependencies:
          - mdformat-gfm
          - mdformat-frontmatter
          - mdformat-mkdocs
        args:
          - --wrap=no
          - --number
        exclude: 'docs/.*\.md'
        # exclude: "README.md|README.zh-CN.md|CONTRIBUTING.md"

  - repo: https://github.com/codespell-project/codespell
    rev: v2.2.6
    hooks:
      - id: codespell
        exclude: "docs/de|docs/fr|docs/pt|docs/es|docs/mkdocs_de.yml"
        args:
          - --ignore-words-list=crate,nd,ned,strack,dota,ane,segway,fo,gool,winn,commend,bloc,nam,afterall

  - repo: https://github.com/hadialqattan/pycln
    rev: v2.4.0
    hooks:
      - id: pycln
        args: [--all]
#
#  - repo: https://github.com/PyCQA/docformatter
#    rev: v1.7.5
#    hooks:
#      - id: docformatter

#  - repo: https://github.com/asottile/yesqa
#    rev: v1.4.0
#    hooks:
#      - id: yesqa

#  - repo: https://github.com/asottile/dead
#    rev: v1.5.0
#    hooks:
#    -   id: dead

#  - repo: https://github.com/ultralytics/pre-commit
#    rev: bd60a414f80a53fb8f593d3bfed4701fc47e4b23
#    hooks:
#      - id: capitalize-comments

---
description: Transform object tracking with Ultralytics YOLO Object Counter featuring cutting-edge technology for precise real-time counting in video streams.
keywords: Ultralytics YOLO, object tracking software, real-time counting solutions, video stream analysis, YOLOv8 object detection, AI surveillance, smart counting technology, computer vision, AI-powered tracking, object counting accuracy, video analytics tools, automated monitoring.
---

# Reference for `ultralytics/solutions/object_counter.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/object_counter.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/object_counter.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/solutions/object_counter.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.solutions.object_counter.ObjectCounter

<br><br>

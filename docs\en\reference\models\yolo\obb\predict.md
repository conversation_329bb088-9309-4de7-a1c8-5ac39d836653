---
description: Discover OBBPredictor for YOLO, specializing in Oriented Bounding Box predictions. Essential for advanced object detection with Ultralytics YOLO.
keywords: Ultralytics, OBBPredictor, YOLO, Oriented Bounding Box, object detection, advanced object detection, YOLO model, deep learning, AI, machine learning, computer vision, OBB detection
---

# Reference for `ultralytics/models/yolo/obb/predict.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/obb/predict.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/obb/predict.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/obb/predict.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.yolo.obb.predict.OBBPredictor

<br><br>

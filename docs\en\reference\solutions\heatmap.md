---
description: Explore Ultralytics YOLO's advanced Heatmaps feature designed to highlight areas of interest, providing an immediate, impactful way to interpret spatial information.
keywords: Ultralytics, YOLO, heatmaps, object tracking, data visualization, real-time tracking, machine learning, object counting, computer vision, retail analytics, YOLOv8, artificial intelligence
---

# Reference for `ultralytics/solutions/heatmap.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/heatmap.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/heatmap.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/solutions/heatmap.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.solutions.heatmap.Heatmap

<br><br>

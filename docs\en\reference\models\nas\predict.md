---
description: Explore Ultralytics NASPredictor. Understand high-level architecture of the model for effective implementation and efficient predictions.
keywords: NASPredictor, Ultralytics, Ultralytics model, model architecture, efficient predictions
---

# Reference for `ultralytics/models/nas/predict.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/nas/predict.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/nas/predict.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/nas/predict.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.nas.predict.NASPredictor

<br><br>

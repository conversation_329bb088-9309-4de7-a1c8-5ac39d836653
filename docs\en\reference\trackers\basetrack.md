---
description: Get familiar with TrackState in Ultralytics. Learn how it is used in the BaseTrack of the Ultralytics tracker for enhanced functionality.
keywords: Ultralytics, TrackState, BaseTrack, Ultralytics tracker, Ultralytics documentation
---

# Reference for `ultralytics/trackers/basetrack.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/basetrack.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/basetrack.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/trackers/basetrack.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.trackers.basetrack.TrackState

<br><br>

## ::: ultralytics.trackers.basetrack.BaseTrack

<br><br>

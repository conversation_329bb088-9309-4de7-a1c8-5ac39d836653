---
description: Learn about the download utilities in Ultralytics YOLO, featuring functions like is_url, check_disk_space, get_github_assets, and download.
keywords: Ultralytics, YOLO, download utilities, is_url, check_disk_space, get_github_assets, download, documentation
---

# Reference for `ultralytics/utils/downloads.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/downloads.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/downloads.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/downloads.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.downloads.is_url

<br><br>

## ::: ultralytics.utils.downloads.delete_dsstore

<br><br>

## ::: ultralytics.utils.downloads.zip_directory

<br><br>

## ::: ultralytics.utils.downloads.unzip_file

<br><br>

## ::: ultralytics.utils.downloads.check_disk_space

<br><br>

## ::: ultralytics.utils.downloads.get_google_drive_file_info

<br><br>

## ::: ultralytics.utils.downloads.safe_download

<br><br>

## ::: ultralytics.utils.downloads.get_github_assets

<br><br>

## ::: ultralytics.utils.downloads.attempt_download_asset

<br><br>

## ::: ultralytics.utils.downloads.download

<br><br>

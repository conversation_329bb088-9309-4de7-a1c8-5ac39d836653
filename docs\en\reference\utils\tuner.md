---
description: Learn to utilize the run_ray_tune function with Ultralytics. Make your machine learning tuning process easier and more efficient.
keywords: Ultralytics, run_ray_tune, machine learning tuning, machine learning efficiency
---

# Reference for `ultralytics/utils/tuner.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/tuner.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/tuner.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/tuner.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.tuner.run_ray_tune

<br><br>

---
description: Browse through Ultralytics YOLO docs to learn about important logging and callback functions used in training and pretraining models.
keywords: Ultralytics, YOLO, callbacks, logger, training, pretraining, machine learning, models
---

# Reference for `ultralytics/utils/callbacks/dvc.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/dvc.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/dvc.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/callbacks/dvc.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.callbacks.dvc._log_images

<br><br>

## ::: ultralytics.utils.callbacks.dvc._log_plots

<br><br>

## ::: ultralytics.utils.callbacks.dvc._log_confusion_matrix

<br><br>

## ::: ultralytics.utils.callbacks.dvc.on_pretrain_routine_start

<br><br>

## ::: ultralytics.utils.callbacks.dvc.on_pretrain_routine_end

<br><br>

## ::: ultralytics.utils.callbacks.dvc.on_train_start

<br><br>

## ::: ultralytics.utils.callbacks.dvc.on_train_epoch_start

<br><br>

## ::: ultralytics.utils.callbacks.dvc.on_fit_epoch_end

<br><br>

## ::: ultralytics.utils.callbacks.dvc.on_train_end

<br><br>

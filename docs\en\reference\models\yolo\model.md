---
description: Discover the Ultralytics YOLO model class. Learn advanced techniques, tips, and tricks for training.
keywords: Ultralytics YOLO, YOLO, YOLO model, Model Training, Machine Learning, Deep Learning, Computer Vision
---

# Reference for `ultralytics/models/yolo/model.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/model.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/model.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/model.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.yolo.model.YOLO

<br><br>

## ::: ultralytics.models.yolo.model.YOLOWorld

<br><br>

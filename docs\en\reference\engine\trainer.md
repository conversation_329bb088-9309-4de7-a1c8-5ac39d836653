---
description: Learn about the BaseTrainer class in the Ultralytics library. From training control, customization to advanced usage.
keywords: Ultralytics, BaseTrainer, Machine Learning, Training Control, Python library
---

# Reference for `ultralytics/engine/trainer.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/engine/trainer.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/engine/trainer.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/engine/trainer.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.engine.trainer.BaseTrainer

<br><br>

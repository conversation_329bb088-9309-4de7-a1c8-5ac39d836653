---
description: Explore Ultralytics YOLO's advanced AI Gym feature for real-time pose estimation and gym exercise tracking using cutting-edge machine learning technology.
keywords: Ultralytics, YOLO, AI Gym, pose estimation, real-time tracking, machine learning, exercise counting, AI fitness, computer vision, gym workout analysis, YOLOv8, artificial intelligence, fitness technology
---

# Reference for `ultralytics/solutions/ai_gym.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/ai_gym.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/ai_gym.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/solutions/ai_gym.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.solutions.ai_gym.AIGym

<br><br>

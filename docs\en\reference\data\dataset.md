---
description: Explore the YOLODataset and SemanticDataset classes in YOLO data. Learn how to efficiently handle and manipulate your data with Ultralytics.
keywords: Ultralytics, YOLO, YOLODataset, SemanticDataset, data handling, data manipulation
---

# Reference for `ultralytics/data/dataset.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/dataset.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/dataset.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/data/dataset.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.data.dataset.YOLODataset

<br><br>

## ::: ultralytics.data.dataset.ClassificationDataset

<br><br>

## ::: ultralytics.data.dataset.SemanticDataset

<br><br>

## ::: ultralytics.data.dataset.load_dataset_cache_file

<br><br>

## ::: ultralytics.data.dataset.save_dataset_cache_file

<br><br>

---
description: Learn about Ultralytics' advanced OBBValidator, an extension of YOLO object detection for oriented bounding box validation.
keywords: Ultralytics, YOLO, OBBValidator, object detection, oriented bounding box, OBB, machine learning, AI, deep learning, Python, YOLO model, image processing, computer vision, YOLO object detection
---

# Reference for `ultralytics/models/yolo/obb/val.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/obb/val.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/obb/val.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/obb/val.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.yolo.obb.val.OBBValidator

<br><br>

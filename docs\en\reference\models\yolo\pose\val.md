---
description: Explore the Po<PERSON>Validator—review how Ultralytics YOLO validates poses for object detection. Improve your understanding of YOLO.
keywords: PoseValidator, Ultralytics, YOLO, Object detection, Pose validation
---

# Reference for `ultralytics/models/yolo/pose/val.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/pose/val.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/pose/val.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/pose/val.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.yolo.pose.val.PoseValidator

<br><br>

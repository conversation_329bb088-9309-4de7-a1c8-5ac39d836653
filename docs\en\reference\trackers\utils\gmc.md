---
description: Explore the Ultralytics GMC tool in our comprehensive documentation. Learn how it works, best practices, and implementation advice.
keywords: Ultralytics, GMC utility, Ultralytics documentation, Ultralytics tracker, machine learning tools
---

# Reference for `ultralytics/trackers/utils/gmc.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/utils/gmc.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/utils/gmc.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/trackers/utils/gmc.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.trackers.utils.gmc.GMC

<br><br>

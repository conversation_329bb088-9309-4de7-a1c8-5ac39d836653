---
description: Explore Ultralytics YOLO metrics tools - from confusion matrix, detection metrics, pose metrics to box IoU. Learn how to compute and plot precision-recall curves.
keywords: Ultralytics, YOLO, YOLOv3, YOLOv4, metrics, confusion matrix, detection metrics, pose metrics, box IoU, mask IoU, plot precision-recall curves, compute average precision
---

# Reference for `ultralytics/utils/metrics.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/metrics.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/metrics.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/metrics.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.metrics.ConfusionMatrix

<br><br>

## ::: ultralytics.utils.metrics.Metric

<br><br>

## ::: ultralytics.utils.metrics.DetMetrics

<br><br>

## ::: ultralytics.utils.metrics.SegmentMetrics

<br><br>

## ::: ultralytics.utils.metrics.PoseMetrics

<br><br>

## ::: ultralytics.utils.metrics.ClassifyMetrics

<br><br>

## ::: ultralytics.utils.metrics.OBBMetrics

<br><br>

## ::: ultralytics.utils.metrics.bbox_ioa

<br><br>

## ::: ultralytics.utils.metrics.box_iou

<br><br>

## ::: ultralytics.utils.metrics.bbox_iou

<br><br>

## ::: ultralytics.utils.metrics.mask_iou

<br><br>

## ::: ultralytics.utils.metrics.kpt_iou

<br><br>

## ::: ultralytics.utils.metrics._get_covariance_matrix

<br><br>

## ::: ultralytics.utils.metrics.probiou

<br><br>

## ::: ultralytics.utils.metrics.batch_probiou

<br><br>

## ::: ultralytics.utils.metrics.smooth_BCE

<br><br>

## ::: ultralytics.utils.metrics.smooth

<br><br>

## ::: ultralytics.utils.metrics.plot_pr_curve

<br><br>

## ::: ultralytics.utils.metrics.plot_mc_curve

<br><br>

## ::: ultralytics.utils.metrics.compute_ap

<br><br>

## ::: ultralytics.utils.metrics.ap_per_class

<br><br>

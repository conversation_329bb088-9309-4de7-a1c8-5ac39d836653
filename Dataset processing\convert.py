import os
import cv2
import numpy as np
import yaml
import shutil
from pathlib import Path
import random
from sklearn.model_selection import train_test_split

class MVTecADConverter:
    def __init__(self, input_path, output_path, train_ratio=0.7, val_ratio=0.2, test_ratio=0.1):
        """
        MVTec AD数据集转换器
        
        Args:
            input_path: 原始MVTec AD数据集路径
            output_path: 输出YOLO格式数据集路径
            train_ratio: 训练集比例
            val_ratio: 验证集比例  
            test_ratio: 测试集比例
        """
        self.input_path = Path(input_path)
        self.output_path = Path(output_path)
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = test_ratio
        
        # 确保比例之和为1
        total_ratio = train_ratio + val_ratio + test_ratio
        if abs(total_ratio - 1.0) > 0.001:
            raise ValueError(f"比例之和必须为1.0，当前为{total_ratio}")
        
        # 73类到16类的映射字典
        self.label_mapping = {
            # 结构性裂纹 -> 0
            'capsule_crack': 0, 'hazelnut_crack': 0, 'pill_crack': 0, 
            'tile_crack': 0, 'zipper_broken_teeth': 0, 'zipper_split_teeth': 0,
            
            # 表面划痕 -> 1
            'capsule_scratch': 1, 'metal_nut_scratch': 1, 'pill_scratch': 1,
            'screw_scratch_head': 1, 'screw_scratch_neck': 1, 'wood_scratch': 1,
            
            # 污染物 -> 2
            'bottle_contamination': 2, 'carpet_metal_contamination': 2,
            'grid_metal_contamination': 2, 'pill_contamination': 2,
            
            # 颜色异常 -> 3
            'carpet_color': 3, 'leather_color': 3, 'metal_nut_color': 3,
            'pill_color': 3, 'wood_color': 3,
            
            # 形变缺陷 -> 4
            'grid_bent': 4, 'leather_fold': 4, 'metal_nut_bent': 4,
            'transistor_bent_lead': 4, 'capsule_squeeze': 4, 'zipper_squeezed_teeth': 4,
            
            # 缺失部件 -> 5
            'cable_missing_cable': 5, 'cable_missing_wire': 5,
            
            # 孔洞缺陷 -> 6
            'carpet_hole': 6, 'hazelnut_hole': 6, 'wood_hole': 6,
            'capsule_poke': 6, 'leather_poke': 6,
            
            # 表面涂层缺陷 -> 7
            'grid_glue': 7, 'leather_glue': 7, 'tile_glue_strip': 7,
            'tile_oil': 7, 'wood_liquid': 7,
            
            # 印刷标记缺陷 -> 8
            'capsule_faulty_imprint': 8, 'hazelnut_print': 8, 'pill_faulty_imprint': 8,
            
            # 线程织物缺陷 -> 9
            'carpet_thread': 9, 'grid_thread': 9, 'zipper_fabric_border': 9,
            'zipper_fabric_interior': 9,
            
            # 位置错误 -> 10
            'metal_nut_flip': 10, 'transistor_misplaced': 10, 'screw_manipulated_front': 10,
            
            # 线缆缺陷 -> 11
            'cable_bent_wire': 11, 'cable_cable_swap': 11, 'cable_cut_inner_insulation': 11,
            'cable_cut_outer_insulation': 11, 'cable_poke_insulation': 11, 'transistor_cut_lead': 11,
            
            # 复合缺陷 -> 12
            'cable_combined': 12, 'pill_combined': 12, 'wood_combined': 12, 'zipper_combined': 12,
            
            # 机械损伤 -> 13
            'bottle_broken_large': 13, 'bottle_broken_small': 13, 'grid_broken': 13,
            'hazelnut_cut': 13, 'leather_cut': 13, 'carpet_cut': 13,
            
            # 表面纹理异常 -> 14
            'tile_gray_stroke': 14, 'tile_rough': 14, 'zipper_rough': 14,
            
            # 装配错误 -> 15
            'pill_pill_type': 15, 'screw_thread_side': 15, 'screw_thread_top': 15,
            'toothbrush_defective': 15, 'transistor_damaged_case': 15
        }
        
        # 16类名称
        self.class_names = [
            'structural_crack',    # 0: 结构性裂纹
            'surface_scratch',     # 1: 表面划痕
            'contamination',       # 2: 污染物
            'color_variation',     # 3: 颜色异常
            'deformation',         # 4: 形变缺陷
            'missing_part',        # 5: 缺失部件
            'hole_defect',         # 6: 孔洞缺陷
            'surface_coating',     # 7: 表面涂层缺陷
            'print_marking',       # 8: 印刷标记缺陷
            'thread_fabric',       # 9: 线程织物缺陷
            'position_error',      # 10: 位置错误
            'wire_cable',          # 11: 线缆缺陷
            'combined_multiple',   # 12: 复合缺陷
            'mechanical_damage',   # 13: 机械损伤
            'surface_texture',     # 14: 表面纹理异常
            'assembly_error'       # 15: 装配错误
        ]
        
    def create_output_structure(self):
        """创建输出目录结构"""
        dirs_to_create = [
            'train/images', 'train/labels',
            'val/images', 'val/labels', 
            'test/images', 'test/labels'
        ]
        
        for dir_path in dirs_to_create:
            (self.output_path / dir_path).mkdir(parents=True, exist_ok=True)
            
    def mask_to_bbox(self, mask_path):
        """
        将掩码转换为YOLO格式的边界框
        
        Args:
            mask_path: 掩码文件路径
            
        Returns:
            list: YOLO格式的边界框列表 [class_id, x_center, y_center, width, height]
        """
        try:
            # 读取掩码图像
            mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
            if mask is None:
                print(f"警告: 无法读取掩码文件 {mask_path}")
                return []
            
            h, w = mask.shape
            
            # 找到所有非零像素(缺陷区域)
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            bboxes = []
            for contour in contours:
                # 计算边界框
                x, y, box_w, box_h = cv2.boundingRect(contour)
                
                # 过滤太小的区域
                if box_w < 5 or box_h < 5:
                    continue
                
                # 转换为YOLO格式 (归一化坐标)
                x_center = (x + box_w / 2) / w
                y_center = (y + box_h / 2) / h
                norm_width = box_w / w
                norm_height = box_h / h
                
                # 确保坐标在[0,1]范围内
                x_center = max(0, min(1, x_center))
                y_center = max(0, min(1, y_center))
                norm_width = max(0, min(1, norm_width))
                norm_height = max(0, min(1, norm_height))
                
                bboxes.append([x_center, y_center, norm_width, norm_height])
                
            return bboxes
            
        except Exception as e:
            print(f"处理掩码文件 {mask_path} 时出错: {e}")
            return []
    
    def get_defect_class_id(self, product_name, defect_type):
        """
        根据产品名称和缺陷类型获取类别ID
        
        Args:
            product_name: 产品名称 (如 'bottle', 'cable')
            defect_type: 缺陷类型 (如 'broken_large', 'bent_wire')
            
        Returns:
            int: 类别ID (0-15)
        """
        defect_key = f"{product_name}_{defect_type}"
        
        if defect_key in self.label_mapping:
            return self.label_mapping[defect_key]
        else:
            print(f"警告: 未找到缺陷类型 '{defect_key}' 的映射，跳过")
            return None
    
    def process_dataset(self):
        """处理整个数据集"""
        print("开始处理MVTec AD数据集...")

        self.create_output_structure()
        all_samples = []
        total_images = 0
        total_defects = 0
        class_counts = {i: 0 for i in range(len(self.class_names))}

        for product_dir in self.input_path.iterdir():
            if not product_dir.is_dir() or product_dir.name in ['license.txt', 'readme.txt']:
                continue

            product_name = product_dir.name
            print(f"\n处理产品: {product_name}")

            test_dir = product_dir / 'test'
            ground_truth_dir = product_dir / 'ground_truth'

            if not test_dir.is_dir():
                print(f"  警告: 在 '{product_name}' 中未找到 'test' 文件夹，跳过。")
                continue

            # 遍历 'test' 文件夹中的缺陷类型子文件夹
            for defect_dir in test_dir.iterdir():
                if not defect_dir.is_dir():
                    continue

                defect_type = defect_dir.name
                if defect_type == 'good':
                    continue

                print(f"  处理缺陷类型: {defect_type}")
                class_id = self.get_defect_class_id(product_name, defect_type)
                if class_id is None:
                    continue

                mask_dir = ground_truth_dir / defect_type
                if not mask_dir.is_dir():
                    print(f"    警告: 找不到对应的掩码文件夹: {mask_dir}")
                    continue
                
                product_defect_images = 0
                for img_file in defect_dir.iterdir():
                    # 确保是文件并且是支持的图片格式
                    if not img_file.is_file() or not img_file.name.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp')):
                        continue
                    
                    # 跳过可能存在的掩码文件（尽管在此逻辑下不应该出现）
                    if '_mask' in img_file.name.lower():
                        continue

                    # 掩码文件总是png格式
                    mask_file = mask_dir / f"{img_file.stem}_mask.png"

                    if not mask_file.exists():
                        print(f"    警告: 找不到图像 {img_file.name} 对应的掩码文件: {mask_file}")
                        continue

                    bboxes = self.mask_to_bbox(mask_file)
                    if not bboxes:
                        # 在某些情况下，掩码文件可能存在但内容为空（无缺陷），这很正常，直接跳过
                        continue

                    sample_info = {
                        'image_path': img_file,
                        'class_id': class_id,
                        'bboxes': bboxes,
                        'product': product_name,
                        'defect': defect_type
                    }
                    all_samples.append(sample_info)

                    total_images += 1
                    product_defect_images += 1
                    total_defects += len(bboxes)
                    class_counts[class_id] += len(bboxes)

                if product_defect_images > 0:
                    print(f"    处理完成: {product_defect_images} 张图像")

        print(f"\n数据集处理完成:")
        print(f"总图像数: {total_images}")
        print(f"总缺陷数: {total_defects}")
        print(f"类别分布: {class_counts}")

        if not all_samples:
            print("\n警告: 未找到任何有效的缺陷样本，无法进行下一步。请检查输入路径和数据集结构。")
            return 0, 0, {}

        # 分割数据集
        self.split_and_save_dataset(all_samples)
        
        # 生成配置文件
        self.generate_yaml_config()
        
        return total_images, total_defects, class_counts
    
    def split_and_save_dataset(self, all_samples):
        """分割数据集并保存"""
        print(f"\n开始分割数据集...")
        print(f"分割比例 - 训练集: {self.train_ratio}, 验证集: {self.val_ratio}, 测试集: {self.test_ratio}")
        
        # 随机打乱样本
        random.shuffle(all_samples)
        
        # 计算分割点
        n_samples = len(all_samples)
        train_end = int(n_samples * self.train_ratio)
        val_end = train_end + int(n_samples * self.val_ratio)
        
        # 分割样本
        train_samples = all_samples[:train_end]
        val_samples = all_samples[train_end:val_end]
        test_samples = all_samples[val_end:]
        
        print(f"训练集: {len(train_samples)} 张图像")
        print(f"验证集: {len(val_samples)} 张图像")
        print(f"测试集: {len(test_samples)} 张图像")
        
        # 保存各个数据集
        self.save_split('train', train_samples)
        self.save_split('val', val_samples)
        self.save_split('test', test_samples)
    
    def save_split(self, split_name, samples):
        """保存数据集分割"""
        print(f"\n保存{split_name}数据集...")
        
        for i, sample in enumerate(samples):
            # 生成新的文件名，按顺序从0开始编号
            new_name = f"{i:06d}"
            
            # 复制并保存图像文件为png格式
            img_dst = self.output_path / split_name / 'images' / f"{new_name}.png"
            try:
                # 使用OpenCV读取并写入，确保文件是有效的png格式
                image = cv2.imread(str(sample['image_path']))
                if image is None:
                    raise IOError(f"无法读取图像文件: {sample['image_path']}")
                cv2.imwrite(str(img_dst), image)
            except Exception as e:
                # 如果OpenCV处理失败，回退到直接复制
                print(f"警告: 使用OpenCV转换图像失败 ({e}), 将直接复制文件: {sample['image_path']}")
                shutil.copy2(sample['image_path'], img_dst)

            # 生成标签文件
            label_dst = self.output_path / split_name / 'labels' / f"{new_name}.txt"
            with open(label_dst, 'w') as f:
                for bbox in sample['bboxes']:
                    x_center, y_center, width, height = bbox
                    f.write(f"{sample['class_id']} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")
        
        print(f"{split_name}数据集保存完成: {len(samples)} 张图像")
    
    def generate_yaml_config(self):
        """生成YOLO配置文件"""
        config = {
            'path': self.output_path.absolute().as_posix(),
            'train': 'train/images',
            'val': 'val/images',
            'test': 'test/images',
            'nc': len(self.class_names),
            'names': [f'{i}: {name}' for i, name in enumerate(self.class_names)]
        }
        
        yaml_path = self.output_path / 'dataset.yaml'
        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2, sort_keys=False)
        
        print(f"\n配置文件已生成: {yaml_path}")
        
        # 同时保存类别映射信息
        mapping_info = {
            'original_to_new_mapping': self.label_mapping,
            'class_names': {i: name for i, name in enumerate(self.class_names)},
            'total_classes': len(self.class_names)
        }
        
        mapping_path = self.output_path / 'class_mapping.yaml'
        with open(mapping_path, 'w', encoding='utf-8') as f:
            yaml.dump(mapping_info, f, default_flow_style=False, allow_unicode=True, indent=2)
        
        print(f"类别映射文件已生成: {mapping_path}")

def main():
    """主函数"""
    print("=== MVTec AD数据集转换工具 ===\n")
    
    # ===== 配置参数区域 =====
    # 请在这里修改输入和输出路径
    INPUT_PATH = r"D:\YOLO\datasets\MVTec_AD"  # 原始MVTec AD数据集路径
    OUTPUT_PATH = r"D:\YOLO\datasets\MVTec_AD_YOLO"  # 输出YOLO格式数据集路径
    
    # 数据集分割比例 (确保三者之和为1.0)
    TRAIN_RATIO = 0.8  # 训练集比例
    VAL_RATIO = 0.1     # 验证集比例  
    TEST_RATIO = 0.1    # 测试集比例
    # ========================
    
    # 验证路径
    input_path = Path(INPUT_PATH)
    if not input_path.exists():
        print(f"错误: 输入路径不存在 - {INPUT_PATH}")
        print("请确认MVTec AD数据集路径是否正确")
        return
    
    output_path = Path(OUTPUT_PATH)
    print(f"输入路径: {INPUT_PATH}")
    print(f"输出路径: {OUTPUT_PATH}")
    print(f"数据集分割比例: 训练集={TRAIN_RATIO}, 验证集={VAL_RATIO}, 测试集={TEST_RATIO}")
    
    # 确认执行
    response = input("\n是否开始转换? (y/N): ")
    if response.lower() != 'y':
        print("转换已取消")
        return
    
    try:
        # 创建转换器并执行转换
        converter = MVTecADConverter(
            input_path=INPUT_PATH,
            output_path=OUTPUT_PATH,
            train_ratio=TRAIN_RATIO,
            val_ratio=VAL_RATIO,
            test_ratio=TEST_RATIO
        )
        
        total_images, total_defects, class_counts = converter.process_dataset()
        
        print("\n" + "="*50)
        print("转换完成!")
        print(f"总共处理图像: {total_images} 张")
        print(f"总共检测缺陷: {total_defects} 个")
        print(f"输出目录: {OUTPUT_PATH}")
        print("\n各类别统计:")
        for class_id, count in class_counts.items():
            if count > 0:
                print(f"  {class_id}: {converter.class_names[class_id]} - {count} 个缺陷")
        print("="*50)
        
    except Exception as e:
        print(f"\n转换过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
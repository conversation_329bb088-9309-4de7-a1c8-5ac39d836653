# 工业缺陷合成与风格迁移系统

## 项目简介
本项目旨在为工业视觉检测任务提供高多样性、高真实性的缺陷样本合成能力。支持两套主流生成体系：
- **UniversalDefectGenerator（主GAN）**：适合高保真、物理一致性缺陷合成。
- **CVAE-GAN + AdaIN风格迁移**：适合多样性增强、风格迁移、跨域缺陷生成。

## 目录结构
```
├── sample_generate/anomaly_generate2.py   # 主训练与生成脚本
├── datasets/                             # 数据集目录（MVTec等）
├── output/                               # 输出目录
│   ├── images/                           # 生成样本图片
│   ├── masks/                            # 生成掩码
│   └── models/                           # 训练好的模型权重
├── README.md                             # 项目说明文档
```

## 主要参数说明
- `MODE`：运行模式，`train`（训练）或`generate`（生成）
- `MODEL_TYPE`：模型类型，`gan`（主GAN）或`cvaegan`（CVAE-GAN+风格迁移）
- `PATCH_SIZE`：训练/生成patch分辨率，建议如(256,256)
- `SAMPLES_PER_IMAGE`：每张正常图像生成的缺陷样本数
- 其余路径、批次、学习率等参数可在脚本头部直接配置

## 训练与生成流程
### 1. 训练
```python
MODE = "train"         # 训练模式
MODEL_TYPE = "gan"     # 或 "cvaegan"
```
- GAN模式：训练主生成器，适合高保真缺陷合成
- CVAE-GAN模式：训练条件VAE-GAN+风格迁移，适合多样性和风格迁移

### 2. 生成
```python
MODE = "generate"      # 生成模式
MODEL_TYPE = "gan"     # 或 "cvaegan"
```
- GAN模式：批量生成合成缺陷样本，自动保存图片和掩码
- CVAE-GAN模式：自动从缺陷库采样真实mask和style_patch，融合到正常图像，提升多样性

## 数据采样与增强策略
- **中心裁剪**：所有patch均为中心正方形，保证输入一致性
- **mask/style_patch采样**：CVAE-GAN生成时自动从缺陷库采样真实mask和风格区域
- **数据增强**：旋转、缩放、亮度、对比度扰动，提升泛化能力

## 目录与输出说明
- `output/images/`：保存所有生成的合成缺陷图片
- `output/masks/`：保存对应的掩码
- `output/models/`：保存训练好的模型权重

## 常见问题与调优建议
- **训练不收敛/损失异常**：建议减小学习率、增大batch size、检查数据归一化
- **生成样本无缺陷/全黑/全白**：检查mask采样、风格patch采样、模型权重加载
- **多样性不足**：增大SAMPLES_PER_IMAGE、丰富缺陷库、调整mask采样比例
- **推理慢/显存溢出**：减小PATCH_SIZE、batch size，或使用更小模型

## 参考与致谢
- MVTec AD、YOLO、CVPR相关论文
- 本项目代码结构和采样策略参考了多篇工业缺陷生成文献

---
如有问题或建议，请联系项目维护者。

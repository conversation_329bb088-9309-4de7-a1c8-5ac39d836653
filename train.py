from ultralytics import YOLO
from swanlab.integration.ultralytics import add_swanlab_callback


if __name__ == "__main__":
    model = YOLO(r"D:\YOLO\YOLOv8_origin\ultralytics\cfg\models\v8\yolov8.yaml")
    model.load()
    # 添加swanlab回调
    add_swanlab_callback(
        model,
        workspace="645068284",
        project="YOLOv8",
        experiment_name="train_yolov8"
    )
    model.train(
        data=r"D:\YOLO\YOLOv8_origin\datasets\MVTec AD\MVTec AD.yaml",
        epochs=100,
        imgsz=320,
        batch=8,
        workers=0,
        project='runs/detect_yolov8',
        name='train',
    )

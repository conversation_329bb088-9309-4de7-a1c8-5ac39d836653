---
description: Dive into the SAM model details in the Ultralytics YOLO documentation. Understand, implement, and optimize your model use.
keywords: Ultralytics, YOLO, SAM Model, Documentations, Machine Learning, AI, Convolutional neural network
---

# Reference for `ultralytics/models/sam/model.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/model.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/model.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/sam/model.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.sam.model.SAM

<br><br>

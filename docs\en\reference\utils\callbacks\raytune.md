---
description: Discover the functionality of the on_fit_epoch_end callback in the Ultralytics YOLO framework. Learn how to end an epoch in your deep learning projects.
keywords: Ultralytics, YOLO, on_fit_epoch_end, callbacks, documentation, deep learning, YOLO framework
---

# Reference for `ultralytics/utils/callbacks/raytune.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/raytune.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/raytune.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/callbacks/raytune.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.callbacks.raytune.on_fit_epoch_end

<br><br>

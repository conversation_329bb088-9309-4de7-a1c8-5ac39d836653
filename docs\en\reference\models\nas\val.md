---
description: Explore the utilities and functions of the Ultralytics NASValidator. Find out how it benefits allocation and optimization in AI models.
keywords: Ultralytics, NASValidator, models.nas.val.NASValidator, AI models, allocation, optimization
---

# Reference for `ultralytics/models/nas/val.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/nas/val.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/nas/val.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/nas/val.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.nas.val.NASValidator

<br><br>

{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "source": ["<div align=\"center\">\n", "\n", "  <a href=\"https://ultralytics.com/yolov8\" target=\"_blank\">\n", "    <img width=\"1024\", src=\"https://raw.githubusercontent.com/ultralytics/assets/main/yolov8/banner-yolov8.png\"></a>\n", "\n", "  [中文](https://docs.ultralytics.com/zh/) | [한국어](https://docs.ultralytics.com/ko/) | [日本語](https://docs.ultralytics.com/ja/) | [Русский](https://docs.ultralytics.com/ru/) | [Deuts<PERSON>](https://docs.ultralytics.com/de/) | [Français](https://docs.ultralytics.com/fr/) | [Español](https://docs.ultralytics.com/es/) | [Português](https://docs.ultralytics.com/pt/) | [हिन्दी](https://docs.ultralytics.com/hi/) | [العربية](https://docs.ultralytics.com/ar/)\n", "\n", "  <a href=\"https://colab.research.google.com/github/ultralytics/ultralytics/blob/main/examples/heatmaps.ipynb\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"></a>\n", "\n", "Welcome to the Ultralytics YOLOv8 🚀 notebook! <a href=\"https://github.com/ultralytics/ultralytics\">YOLOv8</a> is the latest version of the YOLO (You Only Look Once) AI models developed by <a href=\"https://ultralytics.com\">Ultralytics</a>. This notebook serves as the starting point for exploring the <a href=\"https://docs.ultralytics.com/guides/heatmaps/\">heatmaps</a> and understand its features and capabilities.\n", "\n", "YOLOv8 models are fast, accurate, and easy to use, making them ideal for various object detection and image segmentation tasks. They can be trained on large datasets and run on diverse hardware platforms, from CPUs to GPUs.\n", "\n", "We hope that the resources in this notebook will help you get the most out of <a href=\"https://docs.ultralytics.com/guides/heatmaps/\">Ultralytics Heatmaps</a>. Please browse the YOLOv8 <a href=\"https://docs.ultralytics.com/\">Docs</a> for details, raise an issue on <a href=\"https://github.com/ultralytics/ultralytics\">GitHub</a> for support, and join our <a href=\"https://ultralytics.com/discord\">Discord</a> community for questions and discussions!\n", "\n", "</div>"], "metadata": {"id": "PN1cAxdvd61e"}}, {"cell_type": "markdown", "source": ["# Setup\n", "\n", "Pip install `ultralytics` and [dependencies](https://github.com/ultralytics/ultralytics/blob/main/pyproject.toml) and check software and hardware."], "metadata": {"id": "o68Sg1oOeZm2"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "9dSwz_uOReMI"}, "outputs": [], "source": ["!pip install ultralytics"]}, {"cell_type": "markdown", "source": ["# Ultralytics Heatmaps\n", "\n", "Heatmap is color-coded matrix, generated by Ultralytics YOLOv8, simplifies intricate data by using vibrant colors. This visual representation employs warmer hues for higher intensities and cooler tones for lower values. Heatmaps are effective in illustrating complex data patterns, correlations, and anomalies, providing a user-friendly and engaging way to interpret data across various domains."], "metadata": {"id": "m7VkxQ2aeg7k"}}, {"cell_type": "code", "source": ["from ultralytics import YOLO\n", "from ultralytics.solutions import heatmap\n", "import cv2\n", "\n", "model = YOLO(\"yolov8n.pt\")\n", "cap = cv2.VideoCapture(\"path/to/video/file.mp4\")\n", "assert cap.isOpened(), \"Error reading video file\"\n", "w, h, fps = (int(cap.get(x)) for x in (cv2.CAP_PROP_FRAME_WIDTH, cv2.CAP_PROP_FRAME_HEIGHT, cv2.CAP_PROP_FPS))\n", "\n", "# Video writer\n", "video_writer = cv2.VideoWriter(\"heatmap_output.avi\",\n", "                               cv2.VideoWriter_fourcc(*'mp4v'),\n", "                               fps,\n", "                               (w, h))\n", "\n", "# Init heatmap\n", "heatmap_obj = heatmap.Heatmap()\n", "heatmap_obj.set_args(colormap=cv2.COLORMAP_PARULA,\n", "                     imw=w,\n", "                     imh=h,\n", "                     view_img=True,\n", "                     shape=\"circle\")\n", "\n", "while cap.isOpened():\n", "    success, im0 = cap.read()\n", "    if not success:\n", "        print(\"Video frame is empty or video processing has been successfully completed.\")\n", "        break\n", "    tracks = model.track(im0, persist=True, show=False)\n", "\n", "    im0 = heatmap_obj.generate_heatmap(im0, tracks)\n", "    video_writer.write(im0)\n", "\n", "cap.release()\n", "video_writer.release()\n", "cv2.destroyAllWindows()"], "metadata": {"id": "Cx-u59HQdu2o"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["#Community Support\n", "\n", "For more information, you can explore <a href=\"https://docs.ultralytics.com/guides/heatmaps/#heatmap-colormaps\">Ultralytics Heatmaps Docs</a>\n", "\n", "Ultralytics ⚡ resources\n", "- About Us – https://ultralytics.com/about\n", "- Join Our Team – https://ultralytics.com/work\n", "- Contact Us – https://ultralytics.com/contact\n", "- Discord – https://discord.gg/2wNGbc6g9X\n", "- Ultralytics License – https://ultralytics.com/license\n", "\n", "YOLOv8 🚀 resources\n", "- GitHub – https://github.com/ultralytics/ultralytics\n", "- Docs – https://docs.ultralytics.com/"], "metadata": {"id": "QrlKg-y3fEyD"}}]}
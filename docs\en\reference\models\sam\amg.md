---
description: Explore Ultralytics methods for mask data processing, transformation and encoding. Deepen your understanding of RLE encoding, image cropping and more.
keywords: Ultralytics, Mask Data, Transformation, Encoding, RLE encoding, Image cropping, Pytorch, SAM, AMG, Ultralytics model
---

# Reference for `ultralytics/models/sam/amg.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/amg.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/amg.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/sam/amg.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.sam.amg.is_box_near_crop_edge

<br><br>

## ::: ultralytics.models.sam.amg.batch_iterator

<br><br>

## ::: ultralytics.models.sam.amg.calculate_stability_score

<br><br>

## ::: ultralytics.models.sam.amg.build_point_grid

<br><br>

## ::: ultralytics.models.sam.amg.build_all_layer_point_grids

<br><br>

## ::: ultralytics.models.sam.amg.generate_crop_boxes

<br><br>

## ::: ultralytics.models.sam.amg.uncrop_boxes_xyxy

<br><br>

## ::: ultralytics.models.sam.amg.uncrop_points

<br><br>

## ::: ultralytics.models.sam.amg.uncrop_masks

<br><br>

## ::: ultralytics.models.sam.amg.remove_small_regions

<br><br>

## ::: ultralytics.models.sam.amg.batched_mask_to_box

<br><br>

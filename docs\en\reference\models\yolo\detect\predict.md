---
description: Explore the guide to using the DetectionPredictor in Ultralytics YOLO. Learn how to predict, detect and analyze objects accurately.
keywords: Ultralytics, YOLO, DetectionPredictor, detect, predict, object detection, analysis
---

# Reference for `ultralytics/models/yolo/detect/predict.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/detect/predict.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/detect/predict.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/detect/predict.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.yolo.detect.predict.DetectionPredictor

<br><br>

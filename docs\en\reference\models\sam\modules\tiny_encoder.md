---
description: Get in-depth insights about Ultralytics Tiny Encoder Modules such as Conv2d_BN, MBConv, <PERSON>v<PERSON><PERSON><PERSON>, Attention, BasicLayer, and TinyViT. Improve your understanding of machine learning model components.
keywords: Ultralytics, Tiny Encoder, Conv2d_BN, MBConv, Conv<PERSON>ayer, Attention, BasicLayer, TinyViT, Machine learning modules, Ultralytics models
---

# Reference for `ultralytics/models/sam/modules/tiny_encoder.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/tiny_encoder.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/tiny_encoder.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/sam/modules/tiny_encoder.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.Conv2d_BN

<br><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.PatchEmbed

<br><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.MBConv

<br><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.PatchMerging

<br><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.ConvLayer

<br><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.Mlp

<br><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.Attention

<br><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.TinyViTBlock

<br><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.BasicLayer

<br><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.LayerNorm2d

<br><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.TinyViT

<br><br>

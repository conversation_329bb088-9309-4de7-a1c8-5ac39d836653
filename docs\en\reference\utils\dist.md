---
description: Discover the role of dist.find_free_network_port & dist.generate_ddp_command in Ultralytics DDP utilities. Use our guide for efficient deployment.
keywords: Ultralytics, DDP, DDP utility functions, Distributed Data Processing, find free network port, generate DDP command
---

# Reference for `ultralytics/utils/dist.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/dist.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/dist.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/dist.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.dist.find_free_network_port

<br><br>

## ::: ultralytics.utils.dist.generate_ddp_file

<br><br>

## ::: ultralytics.utils.dist.generate_ddp_command

<br><br>

## ::: ultralytics.utils.dist.ddp_cleanup

<br><br>

---
comments: true
description: Comprehensive reference for the Explorer API. Get a brief description of all the main classes utilised for creating and handling the data in the Ultralytics data explorer project.
keywords: Ultralytics, explorer.py, data explorer, Semantic search, vector similarity search, class reference, documentation, ExplorerDataset, Explorer, data handling
---

# Reference for `ultralytics/data/explorer/explorer.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/explorer/explorer.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/explorer/explorer.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/data/explorer/explorer.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.data.explorer.explorer.ExplorerDataset

<br><br>

## ::: ultralytics.data.explorer.explorer.Explorer

<br><br>

# Ultralytics YOLO 🚀, AGPL-3.0 license
# Dependabot for package version updates
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: pip
    directory: "/"
    schedule:
      interval: weekly
      time: "04:00"
    open-pull-requests-limit: 10
    reviewers:
      - glenn-jocher
    labels:
      - dependencies

  - package-ecosystem: github-actions
    directory: "/.github/workflows"
    schedule:
      interval: weekly
      time: "04:00"
    open-pull-requests-limit: 5
    reviewers:
      - glenn-jocher
    labels:
      - dependencies

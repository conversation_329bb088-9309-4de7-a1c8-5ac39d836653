---
description: Explore various Ultralytics convolution modules including Conv2, DWConv, ConvTranspose, GhostConv, Channel Attention and more.
keywords: Ultralytics, Convolution Modules, Conv2, DWConv, ConvTranspose, GhostConv, ChannelAttention, CBAM, autopad
---

# Reference for `ultralytics/nn/modules/conv.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/nn/modules/conv.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/nn/modules/conv.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/nn/modules/conv.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.nn.modules.conv.Conv

<br><br>

## ::: ultralytics.nn.modules.conv.Conv2

<br><br>

## ::: ultralytics.nn.modules.conv.LightConv

<br><br>

## ::: ultralytics.nn.modules.conv.DWConv

<br><br>

## ::: ultralytics.nn.modules.conv.DWConvTranspose2d

<br><br>

## ::: ultralytics.nn.modules.conv.ConvTranspose

<br><br>

## ::: ultralytics.nn.modules.conv.Focus

<br><br>

## ::: ultralytics.nn.modules.conv.GhostConv

<br><br>

## ::: ultralytics.nn.modules.conv.RepConv

<br><br>

## ::: ultralytics.nn.modules.conv.ChannelAttention

<br><br>

## ::: ultralytics.nn.modules.conv.SpatialAttention

<br><br>

## ::: ultralytics.nn.modules.conv.CBAM

<br><br>

## ::: ultralytics.nn.modules.conv.Concat

<br><br>

## ::: ultralytics.nn.modules.conv.autopad

<br><br>

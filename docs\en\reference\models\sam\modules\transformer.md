---
description: Learn about TwoWayTransformer and Attention modules in Ultralytics. Leverage these tools to enhance your AI models.
keywords: Ultralytics, TwoWayTransformer, Attention, AI models, transformers
---

# Reference for `ultralytics/models/sam/modules/transformer.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/transformer.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/transformer.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/sam/modules/transformer.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.sam.modules.transformer.TwoWayTransformer

<br><br>

## ::: ultralytics.models.sam.modules.transformer.TwoWayAttentionBlock

<br><br>

## ::: ultralytics.models.sam.modules.transformer.Attention

<br><br>

---
description: Explore the Ultralytics ClassificationPredictor guide for model prediction and visualization. Build powerful AI models with YOLO.
keywords: Ultralytics, classification predictor, predict, YOLO, AI models, model visualization
---

# Reference for `ultralytics/models/yolo/classify/predict.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/classify/predict.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/classify/predict.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/classify/predict.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.yolo.classify.predict.ClassificationPredictor

<br><br>

---
description: Get practical insights about our SegmentationValidator in YOLO Ultralytics models. Discover functionality details, methods, inputs, and outputs.
keywords: Ultralytics, YOLO, SegmentationValidator, model segmentation, image classification, object detection
---

# Reference for `ultralytics/models/yolo/segment/val.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/segment/val.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/segment/val.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/segment/val.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.yolo.segment.val.SegmentationValidator

<br><br>

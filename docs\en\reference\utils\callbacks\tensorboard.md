---
description: Explore Ultralytics YOLO Docs for a deep understanding of log_scalars, on_batch_end & other callback utilities embedded in the tensorboard module.
keywords: Ultralytics, YOLO, documentation, callback utilities, log_scalars, on_batch_end, tensorboard
---

# Reference for `ultralytics/utils/callbacks/tensorboard.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/tensorboard.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/tensorboard.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/callbacks/tensorboard.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.callbacks.tensorboard._log_scalars

<br><br>

## ::: ultralytics.utils.callbacks.tensorboard._log_tensorboard_graph

<br><br>

## ::: ultralytics.utils.callbacks.tensorboard.on_pretrain_routine_start

<br><br>

## ::: ultralytics.utils.callbacks.tensorboard.on_train_start

<br><br>

## ::: ultralytics.utils.callbacks.tensorboard.on_train_epoch_end

<br><br>

## ::: ultralytics.utils.callbacks.tensorboard.on_fit_epoch_end

<br><br>

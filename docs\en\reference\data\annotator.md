---
description: Enhance your machine learning model with Ultralytics’ auto_annotate function. Simplify data annotation for improved model training.
keywords: Ultralytics, Auto-Annotate, Machine Learning, AI, Annotation, Data Processing, Model Training
---

# Reference for `ultralytics/data/annotator.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/annotator.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/annotator.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/data/annotator.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.data.annotator.auto_annotate

<br><br>

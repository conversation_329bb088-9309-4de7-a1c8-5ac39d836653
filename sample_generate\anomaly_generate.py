import os
import cv2
import glob
import numpy as np
import random
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from torch.nn.utils import spectral_norm  # 添加谱归一化
from torchvision import transforms, models
import matplotlib.pyplot as plt
from tqdm import tqdm
from PIL import Image
import copy
import shutil
import albumentations as A
from albumentations.pytorch import ToTensorV2


class AutoMVTecDatasetCollector:
    """自动递归采集MVTec_AD_mix数据集所有产品类别/缺陷类型/样本三元组"""

    def __init__(self, root_dir):
        self.root_dir = root_dir
        self.triples = []  # (normal_dir, anomaly_img_dir, anomaly_mask_dir, class_name, defect_name)
        self._collect()

    def _collect(self):
        # 首先检查输入路径本身是否是一个产品类别目录
        is_single_class = os.path.isdir(os.path.join(self.root_dir, 'train')) and \
                          os.path.isdir(os.path.join(self.root_dir, 'test'))

        if is_single_class:
            # 单一产品类别模式
            class_name = os.path.basename(self.root_dir)
            self._collect_from_class_path(self.root_dir, class_name)
        else:
            # 根目录模式，遍历所有子目录
            for class_name in os.listdir(self.root_dir):
                class_path = os.path.join(self.root_dir, class_name)
                if os.path.isdir(class_path):
                    self._collect_from_class_path(class_path, class_name)

    def _collect_from_class_path(self, class_path, class_name):
        """从单个产品类别的路径中收集样本三元组"""
        all_normal_dirs = []
        for split in ['train', 'test']:
            normal_candidate_dir = os.path.join(class_path, split, 'good')
            if os.path.isdir(normal_candidate_dir):
                all_normal_dirs.append(normal_candidate_dir)
        
        if not all_normal_dirs:
            return

        gt_dir = os.path.join(class_path, 'ground_truth')
        if not os.path.isdir(gt_dir):
            return

        for split in ['train', 'test']:
            split_path = os.path.join(class_path, split)
            if not os.path.isdir(split_path):
                continue

            for defect_name in os.listdir(split_path):
                if defect_name == 'good':
                    continue

                anomaly_img_dir = os.path.join(split_path, defect_name)
                anomaly_mask_dir = os.path.join(gt_dir, defect_name)

                if os.path.isdir(anomaly_img_dir) and os.path.isdir(anomaly_mask_dir):
                    if any(fname.endswith(('.png', '.jpg', '.jpeg', '.bmp')) for fname in os.listdir(anomaly_img_dir)):
                        representative_normal_dir = all_normal_dirs[0]
                        self.triples.append((representative_normal_dir, anomaly_img_dir, anomaly_mask_dir, class_name, defect_name))


    def get_triples(self):
        return self.triples


class MultiDefectPatchDataset(Dataset):
    """支持多类别多缺陷类型的工业缺陷数据集"""

    def __init__(self, triple_list, patch_size=(128, 128), augment=True):
        self.triple_list = triple_list
        self.patch_size = patch_size if isinstance(patch_size, tuple) else (patch_size, patch_size)
        self.augment = augment
        
        # 标准化变换
        self.normalize = transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
        
        if A and self.augment:
            self.aug_pipeline = A.Compose([
                A.HorizontalFlip(p=0.2),
                A.VerticalFlip(p=0.1),
                A.ShiftScaleRotate(shift_limit=0.01, scale_limit=0.02, rotate_limit=3, p=0.3),
                A.RandomBrightnessContrast(brightness_limit=0.05, contrast_limit=0.05, p=0.2),
            ], additional_targets={'image0': 'image', 'mask': 'mask'})
        else:
            self.aug_pipeline = None

        self.to_tensor = transforms.ToTensor()
        self.samples = []
        self.defect_type_to_id = {}
        self._extract_all_patches()

    def _load_image_list(self, directory):
        exts = ('*.jpg', '*.png', '*.jpeg')
        files = []
        for e in exts:
            files.extend(glob.glob(os.path.join(directory, e)))
        return files

    def _extract_all_patches(self):
        for normal_dir, anomaly_img_dir, anomaly_mask_dir, class_name, defect_name in self.triple_list:
            # 为缺陷类型分配ID
            defect_key = f"{class_name}_{defect_name}"
            if defect_key not in self.defect_type_to_id:
                self.defect_type_to_id[defect_key] = len(self.defect_type_to_id)
            defect_type_id = self.defect_type_to_id[defect_key]

            normal_paths = self._load_image_list(normal_dir)
            anomaly_imgs = self._load_image_list(anomaly_img_dir)
            for img_path in anomaly_imgs:
                base = os.path.basename(img_path)
                name_wo_ext = os.path.splitext(base)[0]
                mask_path = os.path.join(anomaly_mask_dir, f"{name_wo_ext}_mask.png")
                if not os.path.exists(mask_path):
                    continue
                anomaly_img = cv2.imread(img_path)
                anomaly_img = cv2.cvtColor(anomaly_img, cv2.COLOR_BGR2RGB)
                mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
                _, mask_bin = cv2.threshold(mask, 127, 255, cv2.THRESH_BINARY)
                contours, _ = cv2.findContours(mask_bin, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                for cnt in contours:
                    x, y, w, h = cv2.boundingRect(cnt)
                    if w < 20 or h < 20:
                        continue
                    context_x = max(0, x - w // 2)
                    context_y = max(0, y - h // 2)
                    context_w = min(anomaly_img.shape[1] - context_x, w * 2)
                    context_h = min(anomaly_img.shape[0] - context_y, h * 2)
                    anomaly_patch = anomaly_img[context_y:context_y + context_h, context_x:context_x + context_w]
                    mask_patch = mask_bin[context_y:context_y + context_h, context_x:context_x + context_w]
                    normal_path = random.choice(normal_paths)
                    normal_img = cv2.imread(normal_path)
                    normal_img = cv2.cvtColor(normal_img, cv2.COLOR_BGR2RGB)
                    if normal_img.shape[:2] != anomaly_img.shape[:2]:
                        normal_img = cv2.resize(normal_img, (anomaly_img.shape[1], anomaly_img.shape[0]))
                    normal_patch = normal_img[context_y:context_y + context_h, context_x:context_x + context_w]

                    def center_crop_square(img):
                        h, w = img.shape[:2]
                        side = min(h, w)
                        top = (h - side) // 2
                        left = (w - side) // 2
                        return img[top:top + side, left:left + side]

                    anomaly_patch = center_crop_square(anomaly_patch)
                    normal_patch = center_crop_square(normal_patch)
                    mask_patch = center_crop_square(mask_patch)
                    anomaly_patch = cv2.resize(anomaly_patch, self.patch_size)
                    normal_patch = cv2.resize(normal_patch, self.patch_size)
                    mask_patch = cv2.resize(mask_patch, self.patch_size, interpolation=cv2.INTER_NEAREST)
                    
                    # 总是添加原始样本
                    self.samples.append((normal_patch, anomaly_patch, mask_patch, defect_type_id))

                    # 如果启用增强，则生成额外的增强样本
                    if self.aug_pipeline:
                        for _ in range(3): # 生成3个增强版本
                            transformed = self.aug_pipeline(image=anomaly_patch, image0=normal_patch, mask=mask_patch)
                            aug_anomaly = transformed['image']
                            aug_normal = transformed['image0']
                            aug_mask = transformed['mask']
                            self.samples.append((aug_normal, aug_anomaly, aug_mask, defect_type_id))

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        normal_patch, anomaly_patch, mask_patch, defect_type_id = self.samples[idx]

        # 转换为Tensor
        normal = self.to_tensor(normal_patch)
        anomaly = self.to_tensor(anomaly_patch)
        mask = torch.from_numpy(mask_patch / 255.0).float().unsqueeze(0)
        defect_type = torch.tensor(defect_type_id, dtype=torch.long)

        # 标准化
        normal = self.normalize(normal)
        anomaly = self.normalize(anomaly)

        return normal, anomaly, mask, defect_type


# 轻量级注意力模块 - 减少显存使用
class LightweightAttention(nn.Module):
    def __init__(self, in_channels):
        super(LightweightAttention, self).__init__()
        self.in_channels = in_channels
        # 使用更小的降维比例，减少计算量
        self.query_conv = nn.Conv2d(in_channels, in_channels // 16, 1)  # 从//8改为//16
        self.key_conv = nn.Conv2d(in_channels, in_channels // 16, 1)
        self.value_conv = nn.Conv2d(in_channels, in_channels // 4, 1)  # 减少value通道数
        self.out_conv = nn.Conv2d(in_channels // 4, in_channels, 1)  # 输出投影
        self.gamma = nn.Parameter(torch.zeros(1))

    def forward(self, x):
        batch_size, channels, height, width = x.size()

        # 如果特征图太大，使用平均池化降采样
        if height * width > 1024:  # 32x32以上使用降采样
            x_pooled = F.avg_pool2d(x, 2)
            h_p, w_p = x_pooled.size(2), x_pooled.size(3)

            # 在降采样的特征图上计算注意力
            query = self.query_conv(x_pooled).view(batch_size, -1, h_p * w_p).permute(0, 2, 1)
            key = self.key_conv(x_pooled).view(batch_size, -1, h_p * w_p)
            value = self.value_conv(x_pooled).view(batch_size, -1, h_p * w_p)

            # 简化的注意力计算
            attention = torch.bmm(query, key) / (query.size(-1) ** 0.5)
            attention = F.softmax(attention, dim=-1)

            out = torch.bmm(value, attention.permute(0, 2, 1))
            out = out.view(batch_size, -1, h_p, w_p)
            out = self.out_conv(out)

            # 上采样回原始尺寸
            out = F.interpolate(out, size=(height, width), mode='bilinear', align_corners=False)
        else:
            # 对于小特征图，直接计算
            query = self.query_conv(x).view(batch_size, -1, height * width).permute(0, 2, 1)
            key = self.key_conv(x).view(batch_size, -1, height * width)
            value = self.value_conv(x).view(batch_size, -1, height * width)

            attention = torch.bmm(query, key) / (query.size(-1) ** 0.5)
            attention = F.softmax(attention, dim=-1)

            out = torch.bmm(value, attention.permute(0, 2, 1))
            out = out.view(batch_size, -1, height, width)
            out = self.out_conv(out)

        # 残差连接
        out = self.gamma * out + x
        return out

# 谱归一化函数 - 稳定判别器训练
def spectral_norm(module, name='weight', power_iterations=1):
    """应用谱归一化到指定模块"""
    return nn.utils.spectral_norm(module, name, power_iterations)

# 注意力模块 - 增强对重要特征的关注
class ChannelAttention(nn.Module):
    def __init__(self, in_channels, reduction_ratio=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        self.fc = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // reduction_ratio, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // reduction_ratio, in_channels, 1, bias=False)
        )
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return self.sigmoid(out) * x


class SpatialAttention(nn.Module):
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()
        padding = kernel_size // 2
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=padding, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        out = torch.cat([avg_out, max_out], dim=1)
        out = self.conv(out)
        return self.sigmoid(out) * x


# 残差块 - 改进特征提取
class ResidualBlock(nn.Module):
    def __init__(self, in_channels, out_channels, stride=1):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, 3, stride, 1, bias=False)
        self.bn1 = nn.InstanceNorm2d(out_channels)
        self.relu = nn.LeakyReLU(0.2, inplace=True)

        self.conv2 = nn.Conv2d(out_channels, out_channels, 3, 1, 1, bias=False)
        self.bn2 = nn.InstanceNorm2d(out_channels)

        # 跳跃连接
        self.shortcut = nn.Sequential()
        if stride != 1 or in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 1, stride, bias=False),
                nn.InstanceNorm2d(out_channels)
            )

    def forward(self, x):
        residual = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)

        out += self.shortcut(residual)
        out = self.relu(out)

        return out


class ASPP(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(ASPP, self).__init__()
        self.atrous_block1 = nn.Conv2d(in_channels, out_channels, 1, padding=0, dilation=1)
        self.atrous_block6 = nn.Conv2d(in_channels, out_channels, 3, padding=6, dilation=6)
        self.atrous_block12 = nn.Conv2d(in_channels, out_channels, 3, padding=12, dilation=12)
        self.atrous_block18 = nn.Conv2d(in_channels, out_channels, 3, padding=18, dilation=18)
        self.conv_1x1_output = nn.Conv2d(out_channels * 4, out_channels, 1)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        x1 = self.relu(self.atrous_block1(x))
        x2 = self.relu(self.atrous_block6(x))
        x3 = self.relu(self.atrous_block12(x))
        x4 = self.relu(self.atrous_block18(x))
        x = torch.cat([x1, x2, x3, x4], dim=1)
        x = self.conv_1x1_output(x)
        return x


# 改进的特征提取模块 - 基于2024年最新研究
class FeatureExtractor(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(FeatureExtractor, self).__init__()
        self.conv = nn.Conv2d(in_channels, out_channels, 3, 1, 1)
        self.norm = nn.InstanceNorm2d(out_channels)
        self.activation = nn.LeakyReLU(0.2, inplace=True)
        self.residual = ResidualBlock(out_channels, out_channels)
        self.attention = ChannelAttention(out_channels)
        
        # 添加自注意力机制和特征增强模块
        self.feature_enhance = nn.Sequential(
            nn.Conv2d(out_channels, out_channels, 1),
            nn.InstanceNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, 3, 1, 1),
            nn.InstanceNorm2d(out_channels)
        )
        
        # 使用简单的卷积替代注意力机制以节省显存
        self.simple_enhance = nn.Sequential(
            nn.Conv2d(out_channels, out_channels, 3, 1, 1),
            nn.InstanceNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        out = self.activation(self.norm(self.conv(x)))
        out = self.residual(out)
        out = self.attention(out)

        # 使用简单的卷积增强替代自注意力
        enhanced_simple = self.simple_enhance(out)
        out = out + enhanced_simple * 0.1

        # 特征增强残差连接
        enhanced = self.feature_enhance(out)
        out = out + enhanced * 0.1  # 轻微的特征增强

        return out

# 通用工业缺陷生成器 - 基于2024年最新研究优化的架构，增强颜色稳定性
# 条件缺陷生成器 - 支持缺陷类型条件控制
class UniversalDefectGenerator(nn.Module):
    def __init__(self, latent_dim=100, num_defect_types=10):
        super(UniversalDefectGenerator, self).__init__()
        self.latent_dim = latent_dim
        self.num_defect_types = num_defect_types

        # 缺陷类型嵌入层
        self.defect_embedding = nn.Embedding(num_defect_types, 64)

        # 增强的编码器 - 更强的特征提取能力
        self.encoder_conv1 = nn.Sequential(
            nn.Conv2d(3, 64, 4, 2, 1),
            nn.LeakyReLU(0.2, inplace=True),
        )
        self.encoder_conv2 = nn.Sequential(
            nn.Conv2d(64, 128, 4, 2, 1),
            nn.InstanceNorm2d(128),
            nn.LeakyReLU(0.2, inplace=True),
            # 添加残差连接增强特征
            ResidualBlock(128, 128),
        )
        self.encoder_conv3 = nn.Sequential(
            nn.Conv2d(128, 256, 4, 2, 1),
            nn.InstanceNorm2d(256),
            nn.LeakyReLU(0.2, inplace=True),
            ResidualBlock(256, 256),
        )
        self.encoder_conv4 = nn.Sequential(
            nn.Conv2d(256, 512, 4, 2, 1),
            nn.InstanceNorm2d(512),
            nn.LeakyReLU(0.2, inplace=True),
            ResidualBlock(512, 512),
        )

        # 条件特征融合层
        self.condition_fusion = nn.Sequential(
            nn.Conv2d(512 + 64, 512, 3, 1, 1),  # 512 + 64(defect_embedding)
            nn.InstanceNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, 3, 1, 1),
            nn.InstanceNorm2d(512),
            nn.ReLU(inplace=True)
        )

        # 增强的掩码预测分支 - 生成清晰的二值掩码
        self.mask_predictor = nn.Sequential(
            nn.utils.spectral_norm(nn.Conv2d(512, 256, 3, 1, 1)),
            nn.InstanceNorm2d(256),
            nn.ReLU(inplace=True),
            nn.utils.spectral_norm(nn.Conv2d(256, 128, 3, 1, 1)),
            nn.InstanceNorm2d(128),
            nn.ReLU(inplace=True),
            nn.utils.spectral_norm(nn.Conv2d(128, 64, 3, 1, 1)),
            nn.InstanceNorm2d(64),
            nn.ReLU(inplace=True),
            # 最后一层输出logits，用于后续的温度退火
            nn.Conv2d(64, 1, 3, 1, 1),
        )

        # 掩码锐化模块 - 生成清晰边缘
        self.mask_sharpener = nn.Sequential(
            nn.Conv2d(1, 32, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 16, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 1, 3, 1, 1),
        )

        # 空间注意力掩码引导模块 - 确保掩码和缺陷的空间一致性
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(1, 64, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 128, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 512, 1, 1, 0),
            nn.Sigmoid()
        )

        # 掩码引导的特征融合模块 - 使用注意力机制
        self.mask_guided_fusion = nn.Sequential(
            nn.Conv2d(512, 512, 3, 1, 1),
            nn.InstanceNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, 3, 1, 1),
            nn.InstanceNorm2d(512),
            nn.ReLU(inplace=True)
        )

        # 超强缺陷特征生成器 - 增强对抗能力和表达能力
        self.defect_up1 = nn.Sequential(
            nn.ConvTranspose2d(512, 256, 4, 2, 1),
            nn.InstanceNorm2d(256),
            nn.ReLU(inplace=True),
            ResidualBlock(256, 256),
            ResidualBlock(256, 256),  # 双重残差增强特征
            ChannelAttention(256),  # 添加通道注意力
            ResidualBlock(256, 256),  # 第三重残差
            # 添加额外的特征增强层
            nn.Conv2d(256, 256, 3, 1, 1),
            nn.InstanceNorm2d(256),
            nn.ReLU(inplace=True),
        )
        self.defect_up2 = nn.Sequential(
            nn.ConvTranspose2d(256, 128, 4, 2, 1),
            nn.InstanceNorm2d(128),
            nn.ReLU(inplace=True),
            ResidualBlock(128, 128),
            ResidualBlock(128, 128),
            ChannelAttention(128),  # 添加通道注意力
            ResidualBlock(128, 128),  # 第三重残差
        )
        self.defect_up3 = nn.Sequential(
            nn.ConvTranspose2d(128, 64, 4, 2, 1),
            nn.InstanceNorm2d(64),
            nn.ReLU(inplace=True),
            ResidualBlock(64, 64),
            ResidualBlock(64, 64),
            ChannelAttention(64),  # 添加通道注意力
            ResidualBlock(64, 64),  # 第三重残差
        )
        self.defect_up4 = nn.Sequential(
            nn.ConvTranspose2d(64, 32, 4, 2, 1),
            nn.InstanceNorm2d(32),
            nn.ReLU(inplace=True),
            ResidualBlock(32, 32),
            ChannelAttention(32),  # 添加通道注意力
            ResidualBlock(32, 32),  # 双重残差
        )

        # 增强的缺陷类型特定分支 - 更强的特征学习
        self.defect_type_branches = nn.ModuleList([
            nn.Sequential(
                ResidualBlock(32, 32),
                nn.Conv2d(32, 32, 3, 1, 1),
                nn.InstanceNorm2d(32),
                nn.ReLU(inplace=True),
                ResidualBlock(32, 32),
                nn.Conv2d(32, 32, 3, 1, 1),
                nn.InstanceNorm2d(32),
                nn.ReLU(inplace=True),
                # 添加注意力机制增强特征
                nn.Conv2d(32, 32, 1, 1, 0),
                nn.Sigmoid()
            ) for _ in range(num_defect_types)
        ])

        # 超强细节生成层 - 多尺度特征融合
        self.detail_enhance = nn.Sequential(
            ResidualBlock(32, 32),
            ResidualBlock(32, 32),  # 双重残差增强
            nn.Conv2d(32, 32, 1, 1, 0),  # 1x1卷积压缩特征
            nn.InstanceNorm2d(32),
            nn.ReLU(inplace=True),
        )

        # 多尺度卷积分支 - 捕获不同尺度的细节
        self.multi_scale_conv = nn.ModuleList([
            nn.Conv2d(32, 16, 3, 1, 1),  # 3x3卷积
            nn.Conv2d(32, 16, 5, 1, 2),  # 5x5卷积
            nn.Conv2d(32, 16, 7, 1, 3),  # 7x7卷积
        ])

        # 特征融合和最终输出
        self.output_layer = nn.Sequential(
            nn.Conv2d(48, 24, 3, 1, 1),  # 融合多尺度特征 (16*3=48)
            nn.InstanceNorm2d(24),
            nn.ReLU(inplace=True),
            ResidualBlock(24, 24),
            nn.Conv2d(24, 12, 3, 1, 1),
            nn.InstanceNorm2d(12),
            nn.ReLU(inplace=True),
            nn.Conv2d(12, 3, 3, 1, 1),
            nn.Tanh()
        )

        # 掩码细化模块 - 上采样掩码到原始分辨率，使用谱归一化
        self.mask_refiner = nn.Sequential(
            # 8x8 -> 16x16
            nn.utils.spectral_norm(nn.ConvTranspose2d(1, 32, 4, 2, 1)),
            nn.InstanceNorm2d(32),
            nn.ReLU(inplace=True),
            # 16x16 -> 32x32
            nn.utils.spectral_norm(nn.ConvTranspose2d(32, 16, 4, 2, 1)),
            nn.InstanceNorm2d(16),
            nn.ReLU(inplace=True),
            # 32x32 -> 64x64
            nn.utils.spectral_norm(nn.ConvTranspose2d(16, 8, 4, 2, 1)),
            nn.InstanceNorm2d(8),
            nn.ReLU(inplace=True),
            # 64x64 -> 128x128
            nn.utils.spectral_norm(nn.ConvTranspose2d(8, 4, 4, 2, 1)),
            nn.InstanceNorm2d(4),
            nn.ReLU(inplace=True),
            # 最终掩码输出层 - 使用小权重初始化
            nn.Conv2d(4, 1, 3, 1, 1),
            # 不添加激活函数，让损失函数处理
        )

        # 温度参数用于掩码锐化 - 优化温度设置生成更清晰掩码
        self.register_parameter('temperature', nn.Parameter(torch.tensor(2.0)))  # 进一步降低初始温度，生成更清晰的掩码

        # 掩码生成稳定性参数 - 优化为生成更清晰的掩码
        self.mask_threshold = 0.4  # 降低阈值，让更多区域被激活
        self.mask_sharpness = 3.0  # 增强锐化强度，生成更清晰的边界

        # 初始化权重
        self._initialize_weights()

    def hard_sigmoid(self, x, temperature=1.0):
        """改进的硬sigmoid函数，生成更清晰的二值掩码"""
        # 使用更稳定的sigmoid变换
        sigmoid_out = torch.sigmoid(x / temperature)
        # 在训练时保持梯度，推理时二值化
        if self.training:
            return sigmoid_out
        else:
            return (sigmoid_out > self.mask_threshold).float()

    def gumbel_sigmoid(self, logits, temperature=1.0, hard=False):
        """改进的Gumbel Sigmoid，更稳定的掩码生成"""
        # 限制logits范围，防止梯度爆炸
        logits = torch.clamp(logits, -5, 5)

        if self.training:
            # 训练时使用温度调节的sigmoid
            probs = torch.sigmoid(logits / max(temperature, 0.1))
            # 添加轻微的锐化，但保持梯度
            return torch.where(probs > 0.5,
                             probs * 1.2,
                             probs * 0.8).clamp(0, 1)
        else:
            # 推理时生成清晰的二值掩码
            return (torch.sigmoid(logits / max(temperature, 0.1)) > self.mask_threshold).float()

    def _initialize_weights(self):
        """使用He初始化和小权重初始化来防止梯度爆炸"""
        for m in self.modules():
            if isinstance(m, (nn.Conv2d, nn.ConvTranspose2d)):
                # 对于掩码相关的层使用更小的初始化
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.InstanceNorm2d, nn.BatchNorm2d)):
                # 检查权重是否存在再初始化
                if m.weight is not None:
                    nn.init.constant_(m.weight, 1)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Embedding):
                nn.init.normal_(m.weight, 0, 0.01)

        # 改进的掩码相关层权重初始化
        for name, module in self.named_modules():
            if 'mask_predictor' in name and isinstance(module, nn.Conv2d):
                # 掩码预测器使用更合理的初始化
                nn.init.xavier_normal_(module.weight, gain=0.5)  # 使用Xavier初始化
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)  # 零偏置，让网络自己学习
            elif 'mask_refiner' in name and isinstance(module, (nn.Conv2d, nn.ConvTranspose2d)):
                # 掩码细化器使用标准初始化
                nn.init.xavier_normal_(module.weight, gain=1.0)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)
            elif 'mask_sharpener' in name and isinstance(module, nn.Conv2d):
                # 掩码锐化器使用小权重初始化
                nn.init.xavier_normal_(module.weight, gain=0.3)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)



    def forward(self, normal_img, defect_type=None, z=None):
        batch_size = normal_img.size(0)

        # 如果没有指定缺陷类型，随机选择
        if defect_type is None:
            defect_type = torch.randint(0, self.num_defect_types, (batch_size,), device=normal_img.device)
        elif isinstance(defect_type, int):
            defect_type = torch.full((batch_size,), defect_type, device=normal_img.device)

        # 1. 分层编码阶段 - 提取多层次特征
        feat1 = self.encoder_conv1(normal_img)  # [B, 64, 64, 64]
        feat2 = self.encoder_conv2(feat1)       # [B, 128, 32, 32]
        feat3 = self.encoder_conv3(feat2)       # [B, 256, 16, 16]
        features = self.encoder_conv4(feat3)    # [B, 512, 8, 8]

        # 2. 缺陷类型嵌入
        defect_embed = self.defect_embedding(defect_type)  # [B, 64]
        defect_embed = defect_embed.unsqueeze(-1).unsqueeze(-1)  # [B, 64, 1, 1]
        defect_embed = defect_embed.expand(-1, -1, features.size(2), features.size(3))  # [B, 64, 8, 8]

        # 3. 条件特征融合
        conditioned_features = torch.cat([features, defect_embed], dim=1)  # [B, 576, 8, 8]
        conditioned_features = self.condition_fusion(conditioned_features)  # [B, 512, 8, 8]

        # 4. 掩码预测阶段 - 基于条件特征生成清晰掩码
        mask_logits_raw = self.mask_predictor(conditioned_features)  # [B, 1, 8, 8]

        # 4.1 掩码锐化处理 - 生成更清晰的掩码
        mask_logits_sharpened = self.mask_sharpener(mask_logits_raw)
        mask_logits = mask_logits_raw + mask_logits_sharpened  # 残差连接

        # 4.2 使用改进的温度退火生成清晰的二值掩码
        current_temp = torch.clamp(self.temperature, 0.5, 3.0)  # 更合理的温度范围
        # 直接使用hard_sigmoid，更稳定
        mask_probs = self.hard_sigmoid(mask_logits, temperature=current_temp)

        # 5. 空间注意力掩码引导 - 确保空间一致性
        spatial_attention_weights = self.spatial_attention(mask_logits)  # [B, 512, 8, 8]
        # 使用掩码注意力权重调制特征
        mask_guided_features = conditioned_features * spatial_attention_weights  # [B, 512, 8, 8]
        fused_features = self.mask_guided_fusion(mask_guided_features)  # [B, 512, 8, 8]

        # 6. 分层缺陷特征生成 - 在每一层都应用掩码引导
        up1 = self.defect_up1(fused_features)    # [B, 256, 16, 16]
        # 上采样掩码到对应分辨率并应用注意力
        mask_16x16 = F.interpolate(mask_logits, size=(16, 16), mode='bilinear', align_corners=False)
        mask_attention_16 = torch.sigmoid(mask_16x16).expand(-1, 256, -1, -1)
        up1 = up1 * mask_attention_16  # 掩码引导的特征调制

        up2 = self.defect_up2(up1)               # [B, 128, 32, 32]
        mask_32x32 = F.interpolate(mask_logits, size=(32, 32), mode='bilinear', align_corners=False)
        mask_attention_32 = torch.sigmoid(mask_32x32).expand(-1, 128, -1, -1)
        up2 = up2 * mask_attention_32  # 掩码引导的特征调制

        up3 = self.defect_up3(up2)               # [B, 64, 64, 64]
        mask_64x64 = F.interpolate(mask_logits, size=(64, 64), mode='bilinear', align_corners=False)
        mask_attention_64 = torch.sigmoid(mask_64x64).expand(-1, 64, -1, -1)
        up3 = up3 * mask_attention_64  # 掩码引导的特征调制

        defect_features = self.defect_up4(up3)   # [B, 32, 128, 128]
        # 最终层也应用掩码引导
        mask_128x128 = F.interpolate(mask_logits, size=(128, 128), mode='bilinear', align_corners=False)
        mask_attention_128 = torch.sigmoid(mask_128x128).expand(-1, 32, -1, -1)
        defect_features = defect_features * mask_attention_128  # 掩码引导的特征调制

        # 7. 缺陷类型特定的特征增强（带注意力机制）
        enhanced_features = torch.zeros_like(defect_features)
        for i in range(batch_size):
            defect_idx = defect_type[i].item()
            branch_output = self.defect_type_branches[defect_idx](defect_features[i:i+1])
            # 注意力权重在最后一层
            attention_weights = branch_output[:, :, :, :]  # 注意力权重
            enhanced_features[i:i+1] = defect_features[i:i+1] * attention_weights

        # 8. 细节增强处理
        detail_features = self.detail_enhance(enhanced_features)  # [B, 32, 128, 128]

        # 9. 多尺度特征提取
        multi_scale_features = []
        for conv in self.multi_scale_conv:
            multi_scale_features.append(conv(detail_features))

        # 10. 融合多尺度特征
        fused_multi_scale = torch.cat(multi_scale_features, dim=1)  # [B, 48, 128, 128]

        # 11. 生成局部缺陷纹理 - 只生成缺陷部分，不是全图
        defect_texture_raw = self.output_layer(fused_multi_scale)  # [B, 3, 128, 128]

        # 11.1 颜色约束：增强缺陷纹理强度，提高可见性
        defect_texture = torch.tanh(defect_texture_raw) * 1.2  # 增强到[-1.2, 1.2]范围，提高缺陷强度

        # 12. 细化掩码到原始分辨率，使用温度退火生成清晰掩码
        refined_mask_logits = self.mask_refiner(mask_logits)  # [B, 1, 128, 128]

        # 12.1 改进的掩码生成策略 - 增强掩码清晰度
        current_temp = torch.clamp(self.temperature, 0.5, 1.5)  # 降低温度范围，生成更清晰的掩码
        mask_weights = self.hard_sigmoid(refined_mask_logits, temperature=current_temp)

        # 12.2 掩码稳定性处理 - 增强掩码对比度
        # 使用更强的锐化处理，生成更清晰的掩码边界
        mask_weights = torch.sigmoid((mask_weights - 0.3) * (self.mask_sharpness * 1.5)) * 0.95 + 0.05

        # 确保掩码区域有足够的激活
        mask_area = torch.sum(mask_weights > 0.5, dim=[2, 3], keepdim=True).float()
        min_mask_area = 64  # 最小掩码面积
        if torch.any(mask_area < min_mask_area):
            # 如果掩码太小，适度增强
            mask_weights = mask_weights * 1.2

        # 13. 平衡的缺陷融合策略 - 在可见性和稳定性间平衡
        # 13.1 适度增强缺陷对比度
        defect_enhanced = torch.tanh(defect_texture * 1.8)  # 适度增强对比度，平衡效果和稳定性

        # 13.2 计算原始图像的颜色统计信息
        original_mean = torch.mean(normal_img, dim=[2, 3], keepdim=True)
        original_std = torch.std(normal_img, dim=[2, 3], keepdim=True)

        # 13.3 对缺陷纹理进行颜色归一化，保持适度对比度
        defect_mean = torch.mean(defect_enhanced, dim=[2, 3], keepdim=True)
        defect_std = torch.std(defect_enhanced, dim=[2, 3], keepdim=True)

        # 颜色匹配但保持适度对比度的缺陷纹理
        normalized_defect = (defect_enhanced - defect_mean) / (defect_std + 1e-8) * (original_std * 1.3) + original_mean

        # 13.4 平衡的融合策略 - 在保持可见性的同时控制损失
        defect_strength = 0.4  # 适中的缺陷强度，平衡可见性和训练稳定性

        # 13.5 基于掩码区域的自适应强度调整 - 适度增强小缺陷
        mask_area = torch.sum(mask_weights, dim=[2, 3], keepdim=True) / (128 * 128)
        # 小掩码区域使用适度增强的缺陷强度
        adaptive_strength = defect_strength * (1.0 + 0.3 / (mask_area + 0.1))

        # 13.6 平衡的多层次缺陷融合
        # 第一层：基础缺陷融合
        base_defect = normal_img + (normalized_defect - normal_img) * mask_weights * adaptive_strength

        # 第二层：适度边缘增强
        edge_enhancement = torch.abs(normalized_defect - normal_img) * mask_weights * 0.2  # 保持适度的边缘增强
        final_img = base_defect + edge_enhancement

        # 第三层：轻微纹理细节增强
        texture_detail = (normalized_defect - original_mean) * mask_weights * 0.1  # 降低纹理增强强度
        final_img = final_img + texture_detail

        # 6. 颜色一致性检查：适度放宽颜色变化限制
        color_diff = torch.abs(final_img - normal_img)
        max_color_change = 0.4  # 适度放宽最大允许的颜色变化
        color_mask = (color_diff > max_color_change).float()

        # 在颜色变化过大的区域，适度减少变化幅度（保持更多缺陷特征）
        final_img = torch.where(color_diff > max_color_change,
                               normal_img + torch.sign(final_img - normal_img) * max_color_change,
                               final_img)

        # 7. 颜色约束：确保生成的图像颜色在合理范围内
        final_img = torch.clamp(final_img, -1.0, 1.0)

        # 8. 缺陷增强处理 - 适度提升缺陷可见性
        # 8.1 轻微局部对比度增强
        local_contrast = torch.abs(final_img - normal_img) * mask_weights
        final_img = final_img + local_contrast * 0.1  # 轻微增强局部对比度，防止过度

        # 8.2 在非掩码区域完全保持原始颜色
        final_img = final_img * mask_weights + normal_img * (1 - mask_weights)

        # 14. 添加空间一致性约束 - 确保掩码边界的平滑过渡
        # 对掩码进行轻微的高斯模糊，实现平滑过渡
        mask_smooth = F.conv2d(mask_weights,
                              torch.ones(1, 1, 3, 3, device=mask_weights.device) / 9,
                              padding=1)

        # 使用平滑掩码进行最终融合，但保持更多缺陷特征
        final_img = normal_img * (1 - mask_smooth * 0.8) + final_img * (mask_smooth * 0.8 + mask_weights * 0.2)

        # 15. 最终处理 - 确保输出稳定
        final_img = torch.clamp(final_img, -1, 1)
        # 掩码输出应该是logits，不需要过度限制范围，让损失函数处理
        refined_mask_logits = torch.clamp(refined_mask_logits, -5, 5)  # 适度限制避免梯度爆炸

        return final_img, refined_mask_logits










def compute_gradient_penalty(discriminator, real_samples, fake_samples, device):
    """
    计算梯度惩罚 - WGAN-GP的核心技术，用于稳定GAN训练
    基于2024年最新研究优化
    """
    batch_size = real_samples.size(0)

    # 随机插值
    alpha = torch.rand(batch_size, 1, 1, 1).to(device)
    interpolates = alpha * real_samples + (1 - alpha) * fake_samples
    interpolates = interpolates.to(device)
    interpolates.requires_grad_(True)

    # 计算判别器对插值样本的输出
    try:
        # 假设判别器需要三个输入：normal_img, anomaly_img, mask
        # 这里我们使用real_samples作为normal_img，interpolates作为anomaly_img
        # mask使用全1矩阵
        mask_shape = (batch_size, 1, interpolates.size(2), interpolates.size(3))
        dummy_mask = torch.ones(mask_shape).to(device)

        d_interpolates_result = discriminator(real_samples, interpolates, dummy_mask)
        if isinstance(d_interpolates_result, tuple):
            d_interpolates = d_interpolates_result[0]  # 取第一个输出
        else:
            d_interpolates = d_interpolates_result
    except Exception as e:
        # 如果出错，返回零梯度惩罚
        return torch.tensor(0.0, device=device)

    # 计算梯度
    gradients = torch.autograd.grad(
        outputs=d_interpolates,
        inputs=interpolates,
        grad_outputs=torch.ones_like(d_interpolates),
        create_graph=True,
        retain_graph=True,
        only_inputs=True
    )[0]

    # 计算梯度惩罚
    gradients = gradients.view(batch_size, -1)
    gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean()

    return gradient_penalty


def compute_gradient_penalty(discriminator, real_samples, fake_samples, device):
    """计算WGAN-GP的梯度惩罚项"""
    batch_size = real_samples.size(0)

    # 随机插值
    alpha = torch.rand(batch_size, 1, 1, 1, device=device)
    interpolates = alpha * real_samples + (1 - alpha) * fake_samples
    interpolates.requires_grad_(True)

    # 计算判别器输出
    d_interpolates = discriminator(interpolates, interpolates)  # 使用相同输入
    if isinstance(d_interpolates, tuple):
        d_interpolates = d_interpolates[0]  # 只取图像判别结果

    # 计算梯度
    gradients = torch.autograd.grad(
        outputs=d_interpolates,
        inputs=interpolates,
        grad_outputs=torch.ones_like(d_interpolates),
        create_graph=True,
        retain_graph=True,
        only_inputs=True
    )[0]

    # 计算梯度惩罚
    gradients = gradients.view(batch_size, -1)
    gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean()

    return gradient_penalty


# 简化的修复函数 - 移除复杂逻辑
def simple_clamp_fix(img_tensor):
    """简单的图像修复 - 只进行基本的数值稳定化"""
    return torch.clamp(img_tensor, -1, 1)


# 改进的工业缺陷判别器 - 基于2024年最新研究
class UniversalDefectDiscriminator(nn.Module):
    def __init__(self):
        super(UniversalDefectDiscriminator, self).__init__()

        # 增强的判别器架构 - 提升判别能力
        self.image_conv_blocks = nn.ModuleList([
            # 第一层: 6 -> 64 (增加通道数，提升表达能力)
            nn.Sequential(
                spectral_norm(nn.Conv2d(6, 64, 4, 2, 1)),
                nn.LeakyReLU(0.2, inplace=True),
                nn.Dropout2d(0.2)  # 减少dropout，保持判别能力
            ),
            # 第二层: 64 -> 128 (增加通道数)
            nn.Sequential(
                spectral_norm(nn.Conv2d(64, 128, 4, 2, 1)),
                nn.InstanceNorm2d(128),
                nn.LeakyReLU(0.2, inplace=True),
                nn.Dropout2d(0.2)
            ),
            # 第三层: 128 -> 256 (增加通道数)
            nn.Sequential(
                spectral_norm(nn.Conv2d(128, 256, 4, 2, 1)),
                nn.InstanceNorm2d(256),
                nn.LeakyReLU(0.2, inplace=True),
                nn.Dropout2d(0.2)
            ),
            # 第四层: 256 -> 512 (进一步增加通道数)
            nn.Sequential(
                spectral_norm(nn.Conv2d(256, 512, 4, 1, 1)),
                nn.InstanceNorm2d(512),
                nn.LeakyReLU(0.2, inplace=True),
                nn.Dropout2d(0.3)
            ),
            # 输出层: 512 -> 1
            nn.Sequential(
                spectral_norm(nn.Conv2d(512, 1, 4, 1, 1))
            )
        ])
        
        # 特征处理 - 匹配第四层的512通道
        self.feature_enhance = nn.Sequential(
            spectral_norm(nn.Conv2d(512, 512, 3, 1, 1)),
            nn.InstanceNorm2d(512),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout2d(0.2),
            spectral_norm(nn.Conv2d(512, 512, 1, 1, 0)),
            nn.InstanceNorm2d(512),
            nn.LeakyReLU(0.2, inplace=True)
        )

        # 边缘检测分支 - 专门检测缺陷边缘
        self.edge_detector = nn.Sequential(
            spectral_norm(nn.Conv2d(512, 256, 3, 1, 1)),  # 从512通道开始
            nn.InstanceNorm2d(256),
            nn.LeakyReLU(0.2, inplace=True),
            spectral_norm(nn.Conv2d(256, 128, 3, 1, 1)),
            nn.InstanceNorm2d(128),
            nn.LeakyReLU(0.2, inplace=True)
        )

        # 增强的掩码判别器 - 提升掩码判别能力
        self.mask_conv_blocks = nn.ModuleList([
            # 第一层: 1 -> 32 (添加谱归一化)
            nn.Sequential(
                spectral_norm(nn.Conv2d(1, 32, 4, 2, 1)),
                nn.LeakyReLU(0.2, inplace=True),
                nn.Dropout2d(0.2)
            ),
            # 第二层: 32 -> 64 (增强特征)
            nn.Sequential(
                spectral_norm(nn.Conv2d(32, 64, 4, 2, 1)),
                nn.InstanceNorm2d(64),
                nn.LeakyReLU(0.2, inplace=True),
                nn.Dropout2d(0.2)
            ),
            # 第三层: 64 -> 128 (增强特征)
            nn.Sequential(
                spectral_norm(nn.Conv2d(64, 128, 4, 2, 1)),
                nn.InstanceNorm2d(128),
                nn.LeakyReLU(0.2, inplace=True),
                nn.Dropout2d(0.2)
            ),
            # 第四层: 128 -> 256 (新增层，增强判别能力)
            nn.Sequential(
                spectral_norm(nn.Conv2d(128, 256, 4, 1, 1)),
                nn.InstanceNorm2d(256),
                nn.LeakyReLU(0.2, inplace=True),
                nn.Dropout2d(0.3)
            ),
            # 输出层: 256 -> 1 (增强输出)
            nn.Sequential(
                spectral_norm(nn.Conv2d(256, 1, 4, 1, 1))
            )
        ])

    def forward(self, normal_img, anomaly_img, mask=None):
        # 图像判别 - 增加特征提取
        x = torch.cat([normal_img, anomaly_img], dim=1)
        feature_maps = []  # 存储中间特征图用于特征匹配损失

        for i, block in enumerate(self.image_conv_blocks):
            x = block(x)
            # 在第四层应用特征增强
            if i == 3:  # 第四层
                enhanced = self.feature_enhance(x)
                x = x + enhanced * 0.1  # 残差连接
            feature_maps.append(x)

        # 全局平均池化，将输出压缩为标量
        img_output = torch.mean(x, dim=[2, 3])  # [batch_size, 1]

        # 掩码判别
        if mask is not None:
            y = mask

            for block in self.mask_conv_blocks:
                y = block(y)

            # 掩码输出也进行全局平均池化
            mask_output = torch.mean(y, dim=[2, 3])  # [batch_size, 1]
            return img_output, mask_output, feature_maps  # 返回特征图用于特征匹配损失

        return img_output


# Dice Loss实现
class DiceLoss(nn.Module):
    def __init__(self, smooth=1e-6):
        super(DiceLoss, self).__init__()
        self.smooth = smooth

    def forward(self, pred, target):
        pred = pred.contiguous().view(-1)
        target = target.contiguous().view(-1)
        intersection = (pred * target).sum()
        dice = (2. * intersection + self.smooth) / (pred.sum() + target.sum() + self.smooth)
        return 1 - dice


# 焦点损失实现 - 处理难样本和类别不平衡
class FocalLoss(nn.Module):
    def __init__(self, alpha=0.25, gamma=2.0, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs, targets):
        # inputs是logits，targets是真实标签
        ce_loss = F.binary_cross_entropy_with_logits(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1-pt)**self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


# 新增：VGG19特征提取器
class VGG19Features(nn.Module):
    def __init__(self, layers=None, requires_grad=False):
        super().__init__()
        # 优化：使用新的weights API加载预训练模型
        weights = models.VGG19_Weights.DEFAULT  # 加载最新推荐的预训练权重
        vgg19 = models.vgg19(weights=weights).features
        # 选择用于计算感知损失的层 (通常是ReLU激活后的层)
        if layers is None:
            # 常用层：relu_1_1, relu_2_1, relu_3_1, relu_4_1, relu_5_1
            self.layers = {'3': 'relu1_1', '8': 'relu2_1', '17': 'relu3_1', '26': 'relu4_1', '35': 'relu5_1'}
        else:
            self.layers = layers

        self.features = nn.Sequential()
        for name, module in vgg19.named_children():
            self.features.add_module(name, module)
            if name in self.layers:
                # 添加占位符，以便获取中间层输出
                self.features.add_module(f"{self.layers[name]}_output", nn.Identity())
                # 如果所有需要的层都已添加，可以提前停止
                # if len(self.features) >= int(max(self.layers.keys(), key=int)) + 2: # +2 for output and identity
                #     break # Optional optimization

        # 冻结VGG参数，不参与训练
        if not requires_grad:
            for param in self.parameters():
                param.requires_grad = False
        self.eval()  # 设置为评估模式

        # 优化：将标准化参数注册为buffer，避免重复创建
        self.register_buffer('mean', torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1))
        self.register_buffer('std', torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1))


    def forward(self, x):
        outputs = {}
        # 标准化输入以匹配VGG预训练时的输入 (使用buffer)
        x = (x - self.mean) / self.std

        last_output = x
        for name, module in self.features.named_children():
            last_output = module(last_output)
            if name.endswith("_output"):
                layer_name = name.replace("_output", "")
                outputs[layer_name] = last_output
        return outputs


# 新增：VGG感知损失类
class VGGPerceptualLoss(nn.Module):
    def __init__(self, feature_layers=None, weights=None, loss_fn=nn.L1Loss(), device='cuda'):
        super().__init__()
        self.feature_extractor = VGG19Features(layers=feature_layers).to(device)
        self.loss_fn = loss_fn
        # 每层特征损失的权重
        if weights is None:
            # 大幅降低感知损失权重，防止损失过高
            self.weights = {'relu1_1': 1.0 / 128, 'relu2_1': 1.0 / 64, 'relu3_1': 1.0 / 32, 'relu4_1': 1.0 / 16,
                            'relu5_1': 1.0 / 8}
        else:
            self.weights = weights

    def forward(self, pred_img, real_img, mask=None):
        # 确保输入在有效范围内
        pred_img = torch.clamp(pred_img, -1, 1)
        real_img = torch.clamp(real_img, -1, 1)
        
        pred_features = self.feature_extractor(pred_img)
        real_features = self.feature_extractor(real_img)
        loss = 0
        valid_layers = 0
        
        for layer_name, weight in self.weights.items():
            # 修正：直接使用weights中的layer_name作为键获取特征图
            pred_feat = pred_features.get(layer_name)
            real_feat = real_features.get(layer_name)
            if pred_feat is None or real_feat is None:
                # print(f"警告: 未能在VGG输出中找到特征层 '{layer_name}'")  # 注释掉UI不需要的调试信息
                continue  # 跳过不存在的层

            # 检查特征图是否有效
            if torch.isnan(pred_feat).any() or torch.isnan(real_feat).any():
                # print(f"警告: 特征层 '{layer_name}' 包含NaN值")  # 注释掉UI不需要的调试信息
                continue
                
            if mask is not None:
                # 将掩码下采样到与特征图相同的大小
                mask_resized = F.interpolate(mask, size=pred_feat.shape[2:], mode='nearest')
                mask_resized = torch.clamp(mask_resized, 0, 1)
                layer_loss = weight * self.loss_fn(pred_feat * mask_resized, real_feat * mask_resized)
            else:
                layer_loss = weight * self.loss_fn(pred_feat, real_feat)
            
            # 检查层损失是否有效
            if not torch.isnan(layer_loss) and not torch.isinf(layer_loss):
                loss += layer_loss
                valid_layers += 1
                
        # 如果所有层都无效，返回零损失
        if valid_layers == 0:
            return torch.tensor(0.0, device=pred_img.device, requires_grad=True)
            
        return loss


# 通用物理损失模块 - 适用于各种缺陷类型
class UniversalDefectLoss(nn.Module):
    def __init__(self):
        super(UniversalDefectLoss, self).__init__()
        self.l1_loss = nn.L1Loss()
        self.bce_loss = nn.BCEWithLogitsLoss()
        self.dice_loss = DiceLoss()

        # 边缘检测卷积核（Sobel算子）用于增强细节学习
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32).unsqueeze(0).unsqueeze(0)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32).unsqueeze(0).unsqueeze(0)
        self.register_buffer('sobel_x', sobel_x.repeat(3, 1, 1, 1))  # 为RGB三通道复制
        self.register_buffer('sobel_y', sobel_y.repeat(3, 1, 1, 1))

        # Laplacian算子用于细节检测
        laplacian = torch.tensor([[0, -1, 0], [-1, 4, -1], [0, -1, 0]], dtype=torch.float32).unsqueeze(0).unsqueeze(0)
        self.register_buffer('laplacian', laplacian.repeat(3, 1, 1, 1))

        # 高频细节检测核
        high_freq = torch.tensor([[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]], dtype=torch.float32).unsqueeze(0).unsqueeze(0)
        self.register_buffer('high_freq', high_freq.repeat(3, 1, 1, 1))

        # 训练阶段跟踪
        self.current_epoch = 0
        self.warmup_epochs = 50  # 预热期
        self.stable_epochs = 100  # 稳定期

        # 初始化损失组件
        self.__init_loss_components__()

    def compute_edge_loss(self, pred_img, target_img):
        """计算超强边缘保持损失，增强细节学习"""
        # 1. Sobel边缘检测
        pred_edge_x = F.conv2d(pred_img, self.sobel_x, padding=1, groups=3)
        pred_edge_y = F.conv2d(pred_img, self.sobel_y, padding=1, groups=3)
        pred_edges = torch.sqrt(pred_edge_x**2 + pred_edge_y**2 + 1e-8)

        target_edge_x = F.conv2d(target_img, self.sobel_x, padding=1, groups=3)
        target_edge_y = F.conv2d(target_img, self.sobel_y, padding=1, groups=3)
        target_edges = torch.sqrt(target_edge_x**2 + target_edge_y**2 + 1e-8)

        # 2. Laplacian细节检测
        pred_laplacian = F.conv2d(pred_img, self.laplacian, padding=1, groups=3)
        target_laplacian = F.conv2d(target_img, self.laplacian, padding=1, groups=3)

        # 3. 高频细节检测
        pred_high_freq = F.conv2d(pred_img, self.high_freq, padding=1, groups=3)
        target_high_freq = F.conv2d(target_img, self.high_freq, padding=1, groups=3)

        # 综合边缘损失
        edge_loss = F.l1_loss(pred_edges, target_edges)
        laplacian_loss = F.l1_loss(pred_laplacian, target_laplacian)
        high_freq_loss = F.l1_loss(pred_high_freq, target_high_freq)

        return edge_loss + 0.5 * laplacian_loss + 0.3 * high_freq_loss

    def __init_loss_components__(self):
        """初始化损失组件"""
        self.focal_loss = FocalLoss(alpha=0.25, gamma=2.0)

        # 优化损失权重 - 平衡重建质量和对抗学习
        self.l1_weight = 1.0       # 适中的像素重建损失权重
        self.percept_weight = 0.05  # 轻微的感知损失权重
        self.bce_weight = 1.0      # 适中的BCE损失权重
        self.dice_weight = 0.5     # 适中的dice损失权重
        self.focal_weight = 0.3    # 适中的焦点损失权重

        # 特征匹配损失权重
        self.feature_matching_weight = 0.05  # 轻微的特征匹配权重

        # 颜色和细节稳定性损失权重
        self.color_stability_weight = 0.1  # 轻微的颜色稳定性权重
        self.edge_weight = 0.3     # 轻微的边缘权重

        # 梯度惩罚权重
        self.gradient_penalty_weight = 10.0  # 标准梯度惩罚权重

        # 保存初始权重，用于重置
        self.initial_bce_weight = self.bce_weight
        self.initial_dice_weight = self.dice_weight
        self.initial_focal_weight = self.focal_weight

        # 损失监控
        self.loss_spike_count = 0
        self.max_allowed_loss = 20.0  # 允许的最大损失值

    def update_epoch(self, epoch):
        """更新当前训练轮次 - 简化权重调整策略"""
        self.current_epoch = epoch
        # 移除复杂的动态权重调整，保持固定权重确保稳定性

    def feature_matching_loss(self, real_features, fake_features):
        """特征匹配损失 - 改善生成器特征学习"""
        loss = 0.0
        for real_feat, fake_feat in zip(real_features, fake_features):
            if real_feat.shape == fake_feat.shape:
                loss += F.l1_loss(fake_feat, real_feat)
        return loss / len(real_features)

    def color_stability_loss(self, pred_img, normal_img):
        """增强的颜色稳定性损失 - 防止不自然的颜色变化"""
        # 1. 强化颜色分布保持
        pred_mean = torch.mean(pred_img, dim=[2, 3], keepdim=True)  # 每个通道的均值
        normal_mean = torch.mean(normal_img, dim=[2, 3], keepdim=True)
        color_mean_loss = F.l1_loss(pred_mean, normal_mean)

        # 2. 颜色方差稳定性损失
        pred_var = torch.var(pred_img, dim=[2, 3], keepdim=True)
        normal_var = torch.var(normal_img, dim=[2, 3], keepdim=True)
        color_var_loss = F.l1_loss(pred_var, normal_var)

        # 3. 颜色范围约束 - 防止超出正常颜色范围
        # 确保生成的图像颜色在[-1, 1]范围内（假设输入已归一化）
        color_range_penalty = torch.mean(torch.clamp(torch.abs(pred_img) - 1.0, min=0.0))

        # 4. 颜色直方图保持损失 - 保持颜色分布的相似性
        # 计算每个通道的颜色分布
        hist_loss = 0.0
        for c in range(pred_img.shape[1]):  # 对每个颜色通道
            pred_channel = pred_img[:, c:c+1, :, :]
            normal_channel = normal_img[:, c:c+1, :, :]

            # 计算通道内的颜色分布差异
            pred_sorted = torch.sort(pred_channel.view(pred_channel.shape[0], -1), dim=1)[0]
            normal_sorted = torch.sort(normal_channel.view(normal_channel.shape[0], -1), dim=1)[0]
            hist_loss += F.l1_loss(pred_sorted, normal_sorted)

        # 5. 颜色梯度平滑性损失 - 防止颜色突变
        pred_grad_x = torch.abs(pred_img[:, :, :, 1:] - pred_img[:, :, :, :-1])
        pred_grad_y = torch.abs(pred_img[:, :, 1:, :] - pred_img[:, :, :-1, :])
        normal_grad_x = torch.abs(normal_img[:, :, :, 1:] - normal_img[:, :, :, :-1])
        normal_grad_y = torch.abs(normal_img[:, :, 1:, :] - normal_img[:, :, :-1, :])

        gradient_loss = F.l1_loss(torch.mean(pred_grad_x), torch.mean(normal_grad_x)) + \
                       F.l1_loss(torch.mean(pred_grad_y), torch.mean(normal_grad_y))

        # 6. 边缘保持损失
        edge_loss = self.compute_edge_loss(pred_img, normal_img)

        # 重新平衡内部权重，防止损失过大
        return (1.0 * color_mean_loss +
                0.5 * color_var_loss +
                0.3 * color_range_penalty +
                0.2 * hist_loss +
                0.1 * gradient_loss +
                0.3 * edge_loss)

    def compute_spatial_consistency_loss(self, pred_img, real_img, pred_mask, real_mask):
        """计算空间一致性损失 - 确保掩码和缺陷的空间对应关系"""
        # 1. 计算图像差异
        img_diff = torch.abs(pred_img - real_img)  # [B, 3, H, W]
        img_diff_gray = torch.mean(img_diff, dim=1, keepdim=True)  # [B, 1, H, W]

        # 2. 归一化图像差异到[0,1]
        img_diff_norm = (img_diff_gray - img_diff_gray.min()) / (img_diff_gray.max() - img_diff_gray.min() + 1e-8)

        # 3. 计算掩码一致性损失 - 图像差异应该与掩码对应
        # 在掩码区域，图像差异应该较大；在非掩码区域，图像差异应该较小
        mask_consistency_loss = F.mse_loss(img_diff_norm, pred_mask)

        # 4. 计算掩码边界一致性 - 掩码边界应该与图像差异边界对应
        # 使用Sobel算子计算边界
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32, device=pred_img.device).unsqueeze(0).unsqueeze(0)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32, device=pred_img.device).unsqueeze(0).unsqueeze(0)

        # 计算掩码边界
        mask_edge_x = F.conv2d(pred_mask, sobel_x, padding=1)
        mask_edge_y = F.conv2d(pred_mask, sobel_y, padding=1)
        mask_edges = torch.sqrt(mask_edge_x**2 + mask_edge_y**2 + 1e-8)

        # 计算图像差异边界
        diff_edge_x = F.conv2d(img_diff_norm, sobel_x, padding=1)
        diff_edge_y = F.conv2d(img_diff_norm, sobel_y, padding=1)
        diff_edges = torch.sqrt(diff_edge_x**2 + diff_edge_y**2 + 1e-8)

        # 边界一致性损失
        edge_consistency_loss = F.mse_loss(mask_edges, diff_edges)

        # 5. 掩码区域内容一致性 - 在掩码区域内，缺陷应该明显
        mask_region_loss = F.l1_loss(pred_mask * img_diff_norm, pred_mask * real_mask)

        # 综合空间一致性损失
        total_spatial_loss = mask_consistency_loss + 0.5 * edge_consistency_loss + 0.3 * mask_region_loss

        return total_spatial_loss

    def gradient_penalty(self, discriminator, real_samples, fake_samples, device):
        """计算梯度惩罚 - WGAN-GP风格"""
        batch_size = real_samples.size(0)
        alpha = torch.rand(batch_size, 1, 1, 1).to(device)

        # 插值样本
        interpolates = alpha * real_samples + (1 - alpha) * fake_samples
        interpolates = interpolates.to(device)
        interpolates.requires_grad_(True)

        # 判别器输出
        d_interpolates = discriminator(interpolates, interpolates)
        if isinstance(d_interpolates, tuple):
            d_interpolates = d_interpolates[0]  # 取第一个输出

        # 计算梯度
        gradients = torch.autograd.grad(
            outputs=d_interpolates,
            inputs=interpolates,
            grad_outputs=torch.ones_like(d_interpolates),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]

        # 梯度惩罚
        gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean()
        return gradient_penalty

    def safe_loss(self, loss, name):
        if torch.isnan(loss) or torch.isinf(loss):
            return torch.tensor(0.0, device=loss.device, requires_grad=True)
        return loss

    def forward(self, pred_img, real_img, pred_mask, real_mask, mask_weight=10.0):
        """综合损失计算"""
        # 数值稳定性检查
        pred_img = torch.clamp(pred_img, -1, 1)
        real_img = torch.clamp(real_img, -1, 1)
        real_mask = torch.clamp(real_mask, 0, 1)

        # 掩码稳定性检查 - 防止掩码预测过于极端
        pred_mask = torch.clamp(pred_mask, -3, 3)  # 限制掩码logits范围，防止梯度爆炸

        # 检查输入是否包含NaN
        if torch.isnan(pred_img).any():
            pred_img = torch.nan_to_num(pred_img, nan=0.0)
        if torch.isnan(real_img).any():
            real_img = torch.nan_to_num(real_img, nan=0.0)
        if torch.isnan(pred_mask).any():
            pred_mask = torch.nan_to_num(pred_mask, nan=0.0)

        # 1. 图像重建损失 (L1)
        pixel_loss = self.safe_loss(self.l1_loss(pred_img, real_img), "pixel")

        # 2. 掩码预测损失 (BCE + Focal Loss) - 增强稳定性和平衡性
        try:
            # 确保real_mask在正确范围内
            real_mask_clamped = torch.clamp(real_mask, 0, 1)

            # 使用平衡的BCE损失，避免类别不平衡问题
            pos_weight = torch.sum(1 - real_mask_clamped) / (torch.sum(real_mask_clamped) + 1e-8)
            pos_weight = torch.clamp(pos_weight, 0.1, 10.0)  # 限制权重范围

            # 使用BCEWithLogitsLoss，内部包含sigmoid，更稳定
            bce_with_logits = nn.BCEWithLogitsLoss(pos_weight=pos_weight)
            mask_loss = self.safe_loss(bce_with_logits(pred_mask, real_mask_clamped), "mask_bce")

            # 添加焦点损失处理难样本
            focal_loss = self.safe_loss(self.focal_loss(pred_mask, real_mask_clamped), "focal")
        except:
            # print("BCE或焦点损失计算出错，使用备用损失")  # 注释掉UI不需要的调试信息
            mask_loss = torch.tensor(0.0, device=pred_mask.device, requires_grad=True)
            focal_loss = torch.tensor(0.0, device=pred_mask.device, requires_grad=True)
        
        # 3. Dice损失 - 注意：Dice损失仍需要sigmoid后的输入
        pred_mask_sigmoid = torch.sigmoid(pred_mask)
        pred_mask_sigmoid = torch.clamp(pred_mask_sigmoid, 1e-7, 1-1e-7)  # 避免除零
        try:
            dice_loss = self.dice_loss(pred_mask_sigmoid, real_mask)
            dice_loss = self.safe_loss(dice_loss, "dice")
        except:
            dice_loss = torch.tensor(0.0, device=pred_mask.device, requires_grad=True)

        # 4. 掩码清晰度损失 - 鼓励生成清晰的二值掩码
        try:
            pred_mask_sigmoid = torch.sigmoid(pred_mask)

            # 4.1 修复二值化损失 - 降低强度防止损失过高
            binary_loss = torch.mean(2 * pred_mask_sigmoid * (1 - pred_mask_sigmoid))  # 降低系数到2

            # 4.2 总变分损失 - 减少噪声，但保持边缘
            tv_loss_h = torch.mean(torch.abs(pred_mask_sigmoid[:, :, 1:, :] - pred_mask_sigmoid[:, :, :-1, :]))
            tv_loss_w = torch.mean(torch.abs(pred_mask_sigmoid[:, :, :, 1:] - pred_mask_sigmoid[:, :, :, :-1]))
            tv_loss = tv_loss_h + tv_loss_w

            # 4.3 修复边缘保持损失 - 降低强度
            edge_loss = -torch.mean(torch.abs(pred_mask_sigmoid[:, :, 1:, :] - pred_mask_sigmoid[:, :, :-1, :]) +
                                   torch.abs(pred_mask_sigmoid[:, :, :, 1:] - pred_mask_sigmoid[:, :, :, :-1]))

            # 4.4 修复掩码对比度损失 - 降低强度
            mask_mean = torch.mean(pred_mask_sigmoid)
            contrast_loss = -torch.mean((pred_mask_sigmoid - mask_mean) ** 2) * 0.1  # 降低对比度损失强度

            # 修复的掩码质量损失 - 大幅降低权重
            mask_quality_loss = binary_loss + 0.01 * tv_loss + 0.02 * edge_loss + 0.01 * contrast_loss
            mask_quality_loss = self.safe_loss(mask_quality_loss, "mask_quality")
        except:
            mask_quality_loss = torch.tensor(0.0, device=pred_mask.device, requires_grad=True)

        # 感知损失暂时禁用
        percept_loss = torch.tensor(0.0, device=pred_img.device, requires_grad=True)

        # 5. 掩码正则化损失 - 修复掩码退化问题
        pred_mask_sigmoid = torch.sigmoid(pred_mask)
        real_mask_normalized = torch.clamp(real_mask, 0, 1)

        # 掩码应该与真实掩码的激活模式相似
        target_activation = torch.mean(real_mask_normalized)  # 真实掩码的激活比例
        pred_activation = torch.mean(pred_mask_sigmoid)

        # 激活比例损失 - 确保预测掩码有合理的激活比例
        activation_loss = torch.abs(pred_activation - target_activation)

        # 掩码连通性损失 - 鼓励生成连续的区域而不是噪声
        # 计算掩码的空间梯度，连续区域的梯度应该较小
        pred_mask_grad_x = torch.abs(pred_mask_sigmoid[:, :, :, 1:] - pred_mask_sigmoid[:, :, :, :-1])
        pred_mask_grad_y = torch.abs(pred_mask_sigmoid[:, :, 1:, :] - pred_mask_sigmoid[:, :, :-1, :])
        connectivity_loss = torch.mean(pred_mask_grad_x) + torch.mean(pred_mask_grad_y)

        # 掩码形状损失 - 鼓励生成类似真实掩码的形状
        shape_loss = self.l1_loss(pred_mask_sigmoid, real_mask_normalized)

        # 组合掩码正则化损失，减少权重避免过度约束
        mask_reg_loss = 0.05 * activation_loss + 0.02 * connectivity_loss + 0.1 * shape_loss

        # 空间一致性损失 - 确保掩码和缺陷的空间对应
        spatial_consistency_loss = self.compute_spatial_consistency_loss(pred_img, real_img, pred_mask_sigmoid, real_mask)

        # 颜色稳定性损失
        try:
            color_stability = self.safe_loss(self.color_stability_loss(pred_img, real_img), "color_stability")
        except:
            color_stability = torch.tensor(0.0, device=pred_img.device, requires_grad=True)

        # 7. 特征匹配损失 - 如果提供了特征图
        feature_matching_loss = torch.tensor(0.0, device=pred_img.device, requires_grad=True)
        if hasattr(self, 'feature_maps_real') and hasattr(self, 'feature_maps_fake'):
            try:
                for real_feat, fake_feat in zip(self.feature_maps_real, self.feature_maps_fake):
                    feature_matching_loss += self.l1_loss(fake_feat.mean(dim=[2, 3]), real_feat.mean(dim=[2, 3]))
                feature_matching_loss = self.safe_loss(feature_matching_loss, "feature_matching")
            except:
                feature_matching_loss = torch.tensor(0.0, device=pred_img.device, requires_grad=True)

        # 边缘保持损失
        try:
            edge_loss = self.safe_loss(self.compute_edge_loss(pred_img, real_img), "edge")
        except:
            edge_loss = torch.tensor(0.0, device=pred_img.device, requires_grad=True)

        # 大幅降低综合损失权重 - 防止G损失暴增
        total_loss = (
                pixel_loss * self.l1_weight +
                mask_loss * self.bce_weight +
                focal_loss * self.focal_weight +
                dice_loss * self.dice_weight +
                mask_quality_loss * 0.02 +  # 大幅降低掩码质量损失权重
                percept_loss * self.percept_weight +
                mask_reg_loss * 0.005 +  # 大幅减少正则化权重
                spatial_consistency_loss * 0.01 +  # 大幅减少空间一致性权重
                color_stability * self.color_stability_weight +
                feature_matching_loss * self.feature_matching_weight +
                edge_loss * self.edge_weight
        )
        
        # 损失监控和自动调整
        if torch.isnan(total_loss) or torch.isinf(total_loss):
            total_loss = torch.tensor(0.5, device=pred_img.device, requires_grad=True)
        elif total_loss.item() < 1e-6:  # 损失过小，可能有问题
            total_loss = torch.tensor(0.01, device=pred_img.device, requires_grad=True)
        elif total_loss.item() > self.max_allowed_loss:  # 损失过大，自动调整权重
            self.loss_spike_count += 1
            if self.loss_spike_count >= 3:  # 连续3次损失过大，降低权重
                self.l1_weight *= 0.8
                self.bce_weight *= 0.8
                self.dice_weight *= 0.8
                self.percept_weight *= 0.8
                self.loss_spike_count = 0

            # 临时限制当前损失
            total_loss = torch.tensor(self.max_allowed_loss, device=pred_img.device, requires_grad=True)

        # 计算掩码质量指标
        pred_mask_sigmoid = torch.sigmoid(pred_mask)
        mask_quality_score = self.compute_mask_quality(pred_mask_sigmoid, real_mask)

        # 返回总损失和各项损失详情
        loss_details = {
            'pixel': pixel_loss.item() if not torch.isnan(pixel_loss) else 0.0,
            'mask_bce': mask_loss.item() if not torch.isnan(mask_loss) else 0.0,
            'focal': focal_loss.item() if not torch.isnan(focal_loss) else 0.0,
            'dice': dice_loss.item() if not torch.isnan(dice_loss) else 0.0,
            'mask_quality_loss': mask_quality_loss.item() if not torch.isnan(mask_quality_loss) else 0.0,
            'perceptual': percept_loss.item() if not torch.isnan(percept_loss) else 0.0,
            'mask_reg': mask_reg_loss.item() if not torch.isnan(mask_reg_loss) else 0.0,
            'spatial_consistency': spatial_consistency_loss.item() if not torch.isnan(spatial_consistency_loss) else 0.0,
            'mask_quality': mask_quality_score.item() if not torch.isnan(mask_quality_score) else 0.0,
            'total': total_loss.item() if not torch.isnan(total_loss) else 0.0
        }

        return total_loss, loss_details

    def compute_mask_quality(self, pred_mask, real_mask):
        """计算掩码质量分数，用于监控掩码退化"""
        pred_mask = torch.clamp(pred_mask, 0, 1)
        real_mask = torch.clamp(real_mask, 0, 1)

        # IoU分数
        intersection = torch.sum(pred_mask * real_mask)
        union = torch.sum(pred_mask) + torch.sum(real_mask) - intersection
        iou = intersection / (union + 1e-8)

        # 形状相似性 - 检查是否生成了连续区域而不是噪声
        pred_binary = (pred_mask > 0.5).float()
        real_binary = (real_mask > 0.5).float()

        # 计算连通区域数量比例
        pred_activation = torch.mean(pred_binary)
        real_activation = torch.mean(real_binary)
        activation_similarity = 1.0 - torch.abs(pred_activation - real_activation)

        # 综合质量分数
        quality_score = 0.7 * iou + 0.3 * activation_similarity
        return quality_score


# 训练GAN模型
def train_defect_gan(dataloader, generator, discriminator, g_optimizer, d_optimizer,
                     criterion, g_scheduler, d_scheduler, epochs=200, save_dir='models',
                     device=None, d_train_ratio=1, g_train_ratio=1):
    print(f"训练设备: {device}")

    os.makedirs(save_dir, exist_ok=True)

    d_loss_history = []
    g_loss_history = []
    mask_quality_history = []  # 新增：掩码质量历史
    adversarial_loss = nn.BCEWithLogitsLoss().to(device)
    g_losses, d_losses = [], []
    d_loss_ma = 0.0
    g_loss_ma = 0.0
    alpha = 0.1

    # 2024年最新技术：指数移动平均（EMA）稳定生成器
    class EMA:
        def __init__(self, model, decay=0.999):
            self.model = model
            self.decay = decay
            self.shadow = {}
            self.backup = {}
            self.register()

        def register(self):
            for name, param in self.model.named_parameters():
                if param.requires_grad:
                    self.shadow[name] = param.data.clone()

        def update(self):
            for name, param in self.model.named_parameters():
                if param.requires_grad:
                    assert name in self.shadow
                    new_average = (1.0 - self.decay) * param.data + self.decay * self.shadow[name]
                    self.shadow[name] = new_average.clone()

        def apply_shadow(self):
            for name, param in self.model.named_parameters():
                if param.requires_grad:
                    assert name in self.shadow
                    self.backup[name] = param.data
                    param.data = self.shadow[name]

        def restore(self):
            for name, param in self.model.named_parameters():
                if param.requires_grad:
                    assert name in self.backup
                    param.data = self.backup[name]
            self.backup = {}

    # 创建生成器的EMA版本
    generator_ema = EMA(generator, decay=0.999)

    # 掩码质量监控参数
    best_mask_quality = 0.0
    mask_quality_patience = 0
    max_mask_quality_patience = 15  # 如果掩码质量连续15个epoch不提升，则调整策略

    # 2024年优化训练策略：更长的预训练期确保基础质量
    pretraining_epochs = 20  # 延长预训练期，让生成器充分学习重建
    warmup_epochs = 35  # 延长warmup期，渐进式引入对抗训练
    print(f"训练阶段: 预训练({pretraining_epochs}) -> Warmup({pretraining_epochs}-{warmup_epochs}) -> 对抗({warmup_epochs}+)")

    # 学习率调度策略 - 防止判别器过强
    initial_g_lr = g_optimizer.param_groups[0]['lr']
    initial_d_lr = d_optimizer.param_groups[0]['lr']

    # 判别器学习率应该比生成器低很多，防止判别器过强
    if initial_d_lr >= initial_g_lr:
        for param_group in d_optimizer.param_groups:
            param_group['lr'] = initial_g_lr * 0.2  # 判别器学习率设为生成器的20%
    else:
        # 即使判别器学习率已经较低，也要进一步降低
        for param_group in d_optimizer.param_groups:
            param_group['lr'] = param_group['lr'] * 0.5

    # 损失平衡监控
    g_loss_spike_count = 0
    max_g_loss_spikes = 3  # 允许的最大G损失暴增次数



    for epoch in range(epochs):
        epoch_g_loss, epoch_d_loss = 0, 0
        progress_bar = tqdm(dataloader, desc=f"Epoch {epoch+1}/{epochs}")

        # 跟踪本epoch是否训练了生成器和判别器
        epoch_trained_generator = False
        epoch_trained_discriminator = False

        # 优化温度退火策略 - 更快生成清晰掩码
        if hasattr(generator, 'temperature'):
            # 更激进的温度退火，快速降到较低温度生成清晰掩码
            target_temp = max(1.5 - epoch * 0.02, 0.5)  # 从1.5快速降到0.5
            generator.temperature.data = torch.tensor(target_temp, device=generator.temperature.device)

        # 修复G损失暴增检测 - 降低阈值，及时处理损失暴增
        if len(g_loss_history) > 10:  # 减少历史长度，更快响应
            recent_g_losses = g_loss_history[-10:]
            avg_recent_g_loss = sum(recent_g_losses) / len(recent_g_losses)

            # 检测G损失暴增 - 降低阈值，及时处理
            if avg_recent_g_loss > 5.0:  # 降低阈值，及时处理损失暴增
                g_loss_spike_count += 1


                # 温和处理：轻微降低判别器学习率
                for param_group in d_optimizer.param_groups:
                    param_group['lr'] *= 0.9  # 更明显的降低

                # 如果暴增次数过多，重置判别器学习率并降低损失权重
                if g_loss_spike_count >= max_g_loss_spikes:
                    for param_group in d_optimizer.param_groups:
                        param_group['lr'] = initial_d_lr * 0.3  # 大幅降低判别器学习率

                    # 临时降低损失权重
                    criterion.l1_weight *= 0.8
                    criterion.bce_weight *= 0.8
                    criterion.dice_weight *= 0.8

                    g_loss_spike_count = 0

        for i, (normal_img, anomaly_img, mask, defect_type) in enumerate(progress_bar):
            normal_img = normal_img.to(device)
            anomaly_img = anomaly_img.to(device)
            mask = mask.to(device)
            defect_type = defect_type.to(device)

            is_pretraining = epoch < pretraining_epochs
            is_warmup = epoch < warmup_epochs


            # 2024年渐进式判别器训练策略
            if is_pretraining:  # 预训练期：不训练判别器
                train_discriminator = False
            elif is_warmup:  # warmup期：渐进式增加判别器训练频率
                # 在warmup期间，逐渐增加判别器训练频率
                warmup_progress = (epoch - pretraining_epochs) / (warmup_epochs - pretraining_epochs)
                if warmup_progress < 0.3:  # 前30%的warmup期，每3步训练一次判别器
                    train_discriminator = (i % 3 == 0)
                elif warmup_progress < 0.7:  # 中间40%，每2步训练一次
                    train_discriminator = (i % 2 == 0)
                else:  # 后30%，每步都训练
                    train_discriminator = True
            else:  # 对抗期：自适应训练频率
                # 基于最近的损失比例调整训练频率
                if len(g_loss_history) >= 5 and len(d_loss_history) >= 5:
                    recent_g = sum(g_loss_history[-5:]) / 5
                    recent_d = sum([d for d in d_loss_history[-5:] if d > 0.001])
                    if recent_d > 0:
                        recent_d = recent_d / len([d for d in d_loss_history[-5:] if d > 0.001])
                        # 如果生成器损失远大于判别器损失，减少判别器训练
                        if recent_g / recent_d > 10:
                            train_discriminator = (i % 2 == 0)  # 每2步训练一次
                        else:
                            train_discriminator = True
                    else:
                        train_discriminator = True
                else:
                    train_discriminator = True

            if train_discriminator:
                d_optimizer.zero_grad()

                # 生成假样本用于判别器训练（完全断开梯度）
                with torch.no_grad():
                    fake_img_d, fake_mask_d = generator(normal_img.clone(), defect_type.clone())
                    fake_img_d = fake_img_d.detach()
                    fake_mask_d = fake_mask_d.detach()

                # 真样本判别
                real_result = discriminator(normal_img.clone(), anomaly_img.clone(), mask.clone())
                if len(real_result) == 3:
                    real_img_pred, real_mask_pred, _ = real_result
                else:
                    real_img_pred, real_mask_pred = real_result

                # 确保输出形状正确 - 判别器现在输出[batch_size, 1]
                real_img_pred = real_img_pred.squeeze()  # 移除最后一维，变成[batch_size]
                real_mask_pred = real_mask_pred.squeeze()

                # 假样本判别
                fake_result = discriminator(normal_img.clone(), fake_img_d, fake_mask_d)
                if len(fake_result) == 3:
                    fake_img_pred, fake_mask_pred, _ = fake_result
                else:
                    fake_img_pred, fake_mask_pred = fake_result

                # 确保输出形状正确
                fake_img_pred = fake_img_pred.squeeze()  # 移除最后一维，变成[batch_size]
                fake_mask_pred = fake_mask_pred.squeeze()

                # 2024年标签平滑技术：防止判别器过度自信
                batch_size = real_img_pred.size(0)
                # 标签平滑：真标签不是完全的1，假标签不是完全的0
                label_smoothing = 0.1
                real_labels = torch.ones(batch_size, device=device) * (1.0 - label_smoothing)
                fake_labels = torch.zeros(batch_size, device=device) + label_smoothing

                # 简化判别器损失计算 - 只使用图像判别损失
                d_real_loss = adversarial_loss(real_img_pred, real_labels)
                d_fake_loss = adversarial_loss(fake_img_pred, fake_labels)

                # 标准GAN损失
                d_loss = d_real_loss + d_fake_loss



                # 反向传播
                d_loss.backward()
                # 适度的梯度裁剪
                torch.nn.utils.clip_grad_norm_(discriminator.parameters(), max_norm=5.0)

                # 2024年判别器稳定化技术
                d_loss_val = d_loss.item()

                # 判别器过强检测：如果判别器损失过低，说明判别器过强
                if d_loss_val < 0.1:
                    # 判别器过强时，降低学习率或跳过更新
                    for param_group in d_optimizer.param_groups:
                        param_group['lr'] *= 0.95  # 动态降低学习率
                    d_optimizer.zero_grad()  # 跳过这次更新
                elif d_loss_val > 5.0:
                    # 判别器损失过大时，使用较小的学习率
                    torch.nn.utils.clip_grad_norm_(discriminator.parameters(), max_norm=0.5)
                    d_optimizer.step()
                else:
                    # 正常情况下的梯度裁剪和更新
                    torch.nn.utils.clip_grad_norm_(discriminator.parameters(), max_norm=1.0)
                    d_optimizer.step()

                epoch_trained_discriminator = True



                # 记录损失历史 - 只记录有意义的判别器损失
                if 'd_loss_history' in locals() and d_loss_val > 0.001:
                    d_loss_history.append(d_loss_val)
                    if len(d_loss_history) > 50:  # 保持最近50个损失值
                        d_loss_history.pop(0)


            else:
                d_loss_val = 0.0  # 不训练判别器时设为0

            # 生成器训练
            if True:
                g_optimizer.zero_grad()

                # 2024年防模式崩塌技术：输入噪声注入
                # 为输入添加小量噪声，增加生成多样性
                noise_strength = 0.02 if is_pretraining else 0.01  # 预训练期用更多噪声
                input_noise = torch.randn_like(normal_img) * noise_strength
                noisy_normal_img = normal_img + input_noise
                noisy_normal_img = torch.clamp(noisy_normal_img, -1, 1)  # 保持在有效范围

                # 生成样本
                fake_img_g, fake_mask_g = generator(noisy_normal_img, defect_type)

                # 简化对抗损失
                if not is_pretraining:  # 预训练期间不计算对抗损失
                    fake_result_g = discriminator(normal_img, fake_img_g, fake_mask_g)
                    if len(fake_result_g) == 3:
                        fake_img_pred_g, _, _ = fake_result_g
                    else:
                        fake_img_pred_g, _ = fake_result_g

                    # 确保输出形状正确
                    fake_img_pred_g = fake_img_pred_g.squeeze()  # 移除最后一维，变成[batch_size]

                    # 标签平滑：生成器希望判别器认为生成的是真的
                    batch_size = fake_img_pred_g.size(0)
                    # 对生成器也使用轻微的标签平滑
                    real_labels_g = torch.ones(batch_size, device=device) * 0.95

                    # 只使用图像对抗损失
                    g_adv_loss = adversarial_loss(fake_img_pred_g, real_labels_g)
                else:
                    g_adv_loss = torch.tensor(0.0, device=device)

                # 重建损失
                g_recon_loss, loss_details = criterion(fake_img_g, anomaly_img, fake_mask_g, mask)

                # 2024年最新稳定化技术：自适应损失权重和渐进式训练
                if is_pretraining:
                    g_loss = g_recon_loss  # 预训练期间只用重建损失
                elif is_warmup:
                    # 渐进式权重调整 - 基于损失比例动态调整
                    warmup_progress = (epoch - pretraining_epochs) / (warmup_epochs - pretraining_epochs)

                    # 自适应权重：基于重建损失的大小调整对抗权重
                    recon_magnitude = g_recon_loss.item()
                    if recon_magnitude > 10.0:  # 重建损失过大时降低对抗权重
                        base_adv_weight = 0.01
                    elif recon_magnitude > 5.0:
                        base_adv_weight = 0.02
                    else:
                        base_adv_weight = 0.05

                    # 渐进式增长
                    adv_weight = base_adv_weight * (1 + warmup_progress)
                    g_loss = g_recon_loss + adv_weight * g_adv_loss
                else:
                    # 对抗期：使用自适应权重防止模式崩塌
                    recon_magnitude = g_recon_loss.item()
                    adv_magnitude = g_adv_loss.item() if g_adv_loss.item() > 0 else 1.0

                    # 动态平衡：保持重建损失和对抗损失的比例
                    if recon_magnitude / adv_magnitude > 20:  # 重建损失过大
                        adv_weight = 0.01
                    elif recon_magnitude / adv_magnitude > 10:
                        adv_weight = 0.02
                    elif recon_magnitude / adv_magnitude > 5:
                        adv_weight = 0.05
                    else:
                        adv_weight = 0.1  # 标准权重

                    g_loss = g_recon_loss + adv_weight * g_adv_loss

                # 2024年最新梯度稳定化技术
                g_loss.backward()

                # 自适应梯度裁剪：基于损失大小调整裁剪阈值
                if g_loss_val > 10.0:
                    clip_norm = 0.5  # 损失过大时强力裁剪
                elif g_loss_val > 5.0:
                    clip_norm = 0.8
                else:
                    clip_norm = 1.0  # 正常裁剪

                # 梯度裁剪和检查
                grad_norm = torch.nn.utils.clip_grad_norm_(generator.parameters(), max_norm=clip_norm)

                # 梯度爆炸检测：如果梯度过大，跳过这次更新
                if grad_norm > 10.0:
                    g_optimizer.zero_grad()  # 清除梯度，跳过更新
                else:
                    g_optimizer.step()
                    # 更新EMA权重（只在成功更新后）
                    generator_ema.update()

                g_loss_val = g_loss.item()
                epoch_trained_generator = True

                # 记录损失历史
                g_loss_history.append(g_loss_val)
                if len(g_loss_history) > 50:
                    g_loss_history.pop(0)
            else:
                g_loss_val = 0.0
                loss_details = {'pixel': 0.0, 'mask_bce': 0.0, 'focal': 0.0, 'dice': 0.0, 'perceptual': 0.0}

            # 更新移动平均损失
            d_loss_ma = alpha * d_loss_val + (1 - alpha) * d_loss_ma
            g_loss_ma = alpha * g_loss_val + (1 - alpha) * g_loss_ma

            # 更新进度条 - 优化显示逻辑
            epoch_g_loss += g_loss_val
            epoch_d_loss += d_loss_val

            # 优化进度条显示 - 避免显示D=0的情况
            if is_pretraining:
                progress_bar.set_description(
                    f"[预训练 {epoch + 1}/{epochs}] [G: {g_loss_val:.4f}] [Pixel: {loss_details['pixel']:.4f}] [MQ: {loss_details['mask_quality']:.3f}] [BCE: {loss_details['mask_bce']:.4f}]"
                )
            elif is_warmup:
                # 计算实际的D损失值，避免显示0
                valid_d_losses = [x for x in d_loss_history[-5:] if x > 0.001] if d_loss_history else []

                # 计算实际的对抗强度
                if is_warmup:
                    warmup_progress = (epoch - pretraining_epochs) / (warmup_epochs - pretraining_epochs)
                    actual_adv_strength = 0.05 + 0.15 * warmup_progress
                else:
                    actual_adv_strength = 0.3

                if d_loss_val > 0.001:
                    # 当前步骤有有效的D损失
                    progress_bar.set_description(
                        f"[Warmup {epoch + 1}/{epochs}] [D: {d_loss_val:.4f}] [G: {g_loss_val:.4f}] [对抗强度: {actual_adv_strength:.2f}] [MQ: {loss_details['mask_quality']:.3f}]"
                    )
                elif valid_d_losses:
                    # 使用历史有效D损失的平均值
                    avg_valid_d_loss = sum(valid_d_losses) / len(valid_d_losses)
                    progress_bar.set_description(
                        f"[Warmup {epoch + 1}/{epochs}] [D: {avg_valid_d_loss:.4f}] [G: {g_loss_val:.4f}] [对抗强度: {actual_adv_strength:.2f}] [MQ: {loss_details['mask_quality']:.3f}]"
                    )
                else:
                    # 没有有效的D损失，不显示D损失
                    progress_bar.set_description(
                        f"[Warmup {epoch + 1}/{epochs}] [G: {g_loss_val:.4f}] [对抗强度: {actual_adv_strength:.2f}] [MQ: {loss_details['mask_quality']:.3f}] [BCE: {loss_details['mask_bce']:.4f}]"
                    )
            else:
                # 对抗期 - 计算实际的D损失值，避免显示0
                valid_d_losses = [x for x in d_loss_history[-5:] if x > 0.001] if d_loss_history else []

                if d_loss_val > 0.001:
                    # 当前步骤有有效的D损失
                    progress_bar.set_description(
                        f"[对抗 {epoch + 1}/{epochs}] [D: {d_loss_val:.4f}] [G: {g_loss_val:.4f}] [对抗强度: 0.30] [MQ: {loss_details['mask_quality']:.3f}]"
                    )
                elif valid_d_losses:
                    # 使用历史有效D损失的平均值
                    avg_valid_d_loss = sum(valid_d_losses) / len(valid_d_losses)
                    progress_bar.set_description(
                        f"[对抗 {epoch + 1}/{epochs}] [D: {avg_valid_d_loss:.4f}] [G: {g_loss_val:.4f}] [对抗强度: 0.30] [MQ: {loss_details['mask_quality']:.3f}]"
                    )
                else:
                    # 没有有效的D损失，不显示D损失
                    progress_bar.set_description(
                        f"[对抗 {epoch + 1}/{epochs}] [G: {g_loss_val:.4f}] [对抗强度: 0.30] [MQ: {loss_details['mask_quality']:.3f}] [BCE: {loss_details['mask_bce']:.4f}]"
                    )

        # 每个epoch记录平均损失 - 处理判别器损失为0的情况
        avg_g_loss = epoch_g_loss / len(dataloader)
        # 只有当判别器损失有意义时才计算平均值
        if epoch_d_loss > 0.001:
            avg_d_loss = epoch_d_loss / len(dataloader)
        else:
            # 使用历史有效损失的平均值，如果没有则设为0
            valid_d_losses = [d for d in d_loss_history if d > 0.001]
            avg_d_loss = sum(valid_d_losses) / len(valid_d_losses) if valid_d_losses else 0.0

        # 计算平均掩码质量 - 使用最后一个batch的掩码质量作为代表
        avg_mask_quality = loss_details.get('mask_quality', 0.0)

        g_losses.append(avg_g_loss)
        d_losses.append(avg_d_loss)

        # 更新损失历史用于动态平衡 - 只记录有意义的判别器损失
        g_loss_history.append(avg_g_loss)
        if avg_d_loss > 0.001:  # 只记录有意义的判别器损失
            d_loss_history.append(avg_d_loss)

        # 掩码质量监控
        if avg_mask_quality > best_mask_quality:
            best_mask_quality = avg_mask_quality
            mask_quality_patience = 0
            # 保存最佳掩码质量的模型
            if epoch > warmup_epochs:  # 只在对抗期保存
                torch.save(generator.state_dict(), f"{save_dir}/generator_best_mask.pth")
        else:
            mask_quality_patience += 1

        # 保持历史记录长度
        if len(g_loss_history) > 20:
            g_loss_history.pop(0)
        if len(d_loss_history) > 20:
            d_loss_history.pop(0)

        # 2024年自适应学习率调度
        if epoch_trained_generator:
            # 基于损失趋势调整生成器学习率
            if len(g_loss_history) >= 10:
                recent_g_losses = g_loss_history[-10:]
                g_loss_trend = sum(recent_g_losses[-5:]) / 5 - sum(recent_g_losses[:5]) / 5

                # 如果损失持续上升，降低学习率
                if g_loss_trend > 1.0:  # 损失上升过快
                    for param_group in g_optimizer.param_groups:
                        param_group['lr'] *= 0.95  # 降低5%
                elif g_loss_trend < -0.5 and avg_g_loss < 2.0:  # 损失下降且不太高，可以稍微提高
                    for param_group in g_optimizer.param_groups:
                        param_group['lr'] = min(param_group['lr'] * 1.02, 0.0002)  # 最多提高到0.0002

            if g_scheduler is not None:
                g_scheduler.step()

        if epoch_trained_discriminator:
            # 判别器学习率自适应调整
            if len(d_loss_history) >= 5:
                recent_d_losses = [d for d in d_loss_history[-5:] if d > 0.001]
                if recent_d_losses:
                    avg_recent_d = sum(recent_d_losses) / len(recent_d_losses)
                    # 如果判别器过强（损失过低），降低学习率
                    if avg_recent_d < 0.3:
                        for param_group in d_optimizer.param_groups:
                            param_group['lr'] *= 0.9
                    # 如果判别器过弱（损失过高），稍微提高学习率
                    elif avg_recent_d > 2.0:
                        for param_group in d_optimizer.param_groups:
                            param_group['lr'] = min(param_group['lr'] * 1.05, 0.0001)

            if d_scheduler is not None:
                d_scheduler.step()

        # 掩码质量恶化检测和应对策略 - 修复权重暴增问题
        if mask_quality_patience >= max_mask_quality_patience and epoch > warmup_epochs:
            # 策略1: 重置掩码相关损失权重到合理范围，而不是无限增长
            criterion.bce_weight = min(criterion.bce_weight * 1.1, 3.0)  # 限制最大值为3.0
            criterion.dice_weight = min(criterion.dice_weight * 1.1, 2.0)  # 限制最大值为2.0
            criterion.focal_weight = min(criterion.focal_weight * 1.05, 1.0)  # 限制最大值为1.0

            # 策略2: 重置patience计数器
            mask_quality_patience = 0

            # 策略3: 如果权重已经很高，重置到初始值
            if criterion.bce_weight >= 2.5 or criterion.dice_weight >= 1.8:
                criterion.bce_weight = criterion.initial_bce_weight
                criterion.dice_weight = criterion.initial_dice_weight
                criterion.focal_weight = criterion.initial_focal_weight

        # 极简epoch信息显示
        if (epoch + 1) % 10 == 0:  # 每10个epoch显示一次
            if avg_d_loss > 0.001:
                print(f"Epoch [{epoch + 1}/{epochs}] - G: {avg_g_loss:.3f}, D: {avg_d_loss:.3f}, 掩码: {avg_mask_quality:.3f}")
            else:
                print(f"Epoch [{epoch + 1}/{epochs}] - G: {avg_g_loss:.3f}, 掩码: {avg_mask_quality:.3f}")

        # 2024年早停机制：防止过拟合和模式崩塌
        early_stop = False
        if epoch > warmup_epochs + 20:  # 在warmup后20个epoch开始检查
            # 检查生成器损失是否持续上升
            if len(g_loss_history) >= 20:
                recent_20 = g_loss_history[-20:]
                first_10_avg = sum(recent_20[:10]) / 10
                last_10_avg = sum(recent_20[-10:]) / 10

                # 如果最近10个epoch的平均损失比前10个高出50%以上，考虑早停
                if last_10_avg > first_10_avg * 1.5 and last_10_avg > 5.0:
                    print(f"\n检测到生成器损失持续上升，触发早停机制")
                    print(f"前10个epoch平均损失: {first_10_avg:.3f}, 后10个epoch平均损失: {last_10_avg:.3f}")
                    early_stop = True

        # 可视化生成的样本
        if (epoch + 1) % 10 == 0 or epoch == epochs - 1 or early_stop:
            # 使用EMA权重进行可视化
            generator_ema.apply_shadow()
            visualize_samples(generator, dataloader, device, epoch + 1, save_dir)
            generator_ema.restore()

        # 如果触发早停，退出训练循环
        if early_stop:
            print(f"在第{epoch + 1}个epoch提前停止训练")
            break

    # 绘制损失曲线
    plt.figure(figsize=(10, 5))
    plt.plot(g_losses, label='Generator Loss')
    plt.plot(d_losses, label='Discriminator Loss')
    plt.legend()
    plt.savefig(f'{save_dir}/loss_curve.png')

    # 应用EMA权重到生成器，用于最终保存
    print("应用EMA权重到生成器...")
    generator_ema.apply_shadow()

    return generator, discriminator


# 可视化生成的样本
def visualize_samples(generator, dataloader, device, epoch, save_dir):
    generator.eval()
    os.makedirs(f"{save_dir}/samples", exist_ok=True)

    with torch.no_grad():
        # 获取一批数据
        normal_img, real_anomaly, mask, defect_type = next(iter(dataloader))
        normal_img = normal_img.to(device)
        real_anomaly = real_anomaly.to(device)
        mask = mask.to(device)
        defect_type = defect_type.to(device)

        # 生成异常样本
        fake_anomaly, fake_mask = generator(normal_img, defect_type)

        # 转回CPU并转换为numpy数组
        normal_img = normal_img.cpu().numpy()
        real_anomaly = real_anomaly.cpu().numpy()
        fake_anomaly = fake_anomaly.cpu().numpy()
        mask = mask.cpu().numpy()
        fake_mask = fake_mask.cpu().numpy()

        # 反归一化
        normal_img = (normal_img * 0.5 + 0.5)
        real_anomaly = (real_anomaly * 0.5 + 0.5)
        fake_anomaly = (fake_anomaly * 0.5 + 0.5)
        
        # 将生成的mask转换为清晰的二值图像
        fake_mask_sigmoid = 1 / (1 + np.exp(-fake_mask))  # numpy版本的sigmoid
        # 使用更严格的阈值生成清晰的二值掩码
        fake_mask_binary = (fake_mask_sigmoid > 0.5).astype(np.float32)
        # 进一步清理噪点
        fake_mask_clean = np.where(fake_mask_sigmoid > 0.7, 1.0,
                                  np.where(fake_mask_sigmoid < 0.3, 0.0, fake_mask_binary))

        # 综合可视化 - 5行展示所有内容
        n_samples = min(4, normal_img.shape[0])
        fig, axs = plt.subplots(5, n_samples, figsize=(n_samples * 3, 15))
        # 处理 n_samples == 1 时 axs 是一维数组的情况
        if n_samples == 1:
            axs = axs.reshape(-1, 1)  # 将一维数组强制变为二维 (rows, 1)

        for i in range(n_samples):
            # 第1行：显示正常图像
            axs[0, i].imshow(np.transpose(normal_img[i], (1, 2, 0)))
            axs[0, i].set_title('Normal')
            axs[0, i].axis('off')

            # 第2行：显示真实异常图像
            axs[1, i].imshow(np.transpose(real_anomaly[i], (1, 2, 0)))
            axs[1, i].set_title('Real Anomaly')
            axs[1, i].axis('off')

            # 第3行：显示生成的异常图像
            axs[2, i].imshow(np.transpose(fake_anomaly[i], (1, 2, 0)))
            axs[2, i].set_title('Generated Anomaly')
            axs[2, i].axis('off')

            # 第4行：显示真实掩码
            axs[3, i].imshow(mask[i].squeeze(), cmap='gray')
            axs[3, i].set_title('Real Mask')
            axs[3, i].axis('off')

            # 第5行：显示生成的掩码（清理后）
            axs[4, i].imshow(fake_mask_clean[i].squeeze(), cmap='gray')
            axs[4, i].set_title('Generated Mask (Clean)')
            axs[4, i].axis('off')

        plt.tight_layout()
        plt.savefig(f"{save_dir}/samples/epoch_{epoch}.png")
        plt.close()

    generator.train()


# 缺陷样本合成器 - 使用训练好的GAN模型
class DefectGANSynthesizer:
    def __init__(self, config):
        self.triples = config['triples']  # [(normal_dir, anomaly_img_dir, anomaly_mask_dir, class_name, defect_name)]
        self.output_root = config['output_root']
        self.model_path = config.get('model_path', 'models/generator.pth')
        self.samples_per_normal_image = config.get('samples_per_normal_image', 5)  # 为每张正常样本生成的数量
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.input_resolution = config.get('input_resolution', (128, 128))
        self.min_defect_ratio = config.get('min_defect_ratio', 0.2)
        self.max_defect_ratio = config.get('max_defect_ratio', 0.7)

        # 创建缺陷类型映射
        self.defect_type_to_id = {}
        for _, _, _, class_name, defect_name in self.triples:
            defect_key = f"{class_name}_{defect_name}"
            if defect_key not in self.defect_type_to_id:
                self.defect_type_to_id[defect_key] = len(self.defect_type_to_id)

        num_defect_types = len(self.defect_type_to_id)
        print(f"生成模式检测到 {num_defect_types} 种缺陷类型: {list(self.defect_type_to_id.keys())}")

        # 加载预训练生成器 - 优化模型检查逻辑
        self.generator = UniversalDefectGenerator(num_defect_types=num_defect_types).to(self.device)
        if os.path.exists(self.model_path):
            try:
                self.generator.load_state_dict(torch.load(self.model_path, map_location=self.device))
                print(f"成功加载预训练模型: {self.model_path}")
            except Exception as e:
                print(f"错误: 无法加载模型文件 {self.model_path}: {e}")
                print("生成模式需要训练好的模型，请先运行训练模式")
                exit(1)
        else:
            print(f"错误: 模型文件不存在: {self.model_path}")
            print("生成模式需要训练好的模型，请先运行训练模式")
            exit(1)
        self.generator.eval()

        self.transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
        ])

    def _load_image_list(self, directory):
        exts = ('*.jpg', '*.png', '*.jpeg')
        files = []
        for e in exts:
            files.extend(glob.glob(os.path.join(directory, e)))
        return files

    def random_submask_within(self, full_mask, min_ratio=None, max_ratio=None):
        # 优化：通过可控的形态学操作确保缺陷面积比例符合要求
        if min_ratio is None:
            min_ratio = self.min_defect_ratio
        if max_ratio is None:
            max_ratio = self.max_defect_ratio
        mask = full_mask.copy()
        h, w = mask.shape
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            return mask
        
        # 选择最大轮廓以处理
        cnt = max(contours, key=cv2.contourArea)
        mask_single = np.zeros_like(mask)
        cv2.drawContours(mask_single, [cnt], 0, 255, -1)
        area = cv2.countNonZero(mask_single)
        if area == 0:
            return mask_single

        target_ratio = random.uniform(min_ratio, max_ratio)
        target_area = target_ratio * area
        sub_mask = mask_single.copy()
        current_area = area

        # 通过迭代腐蚀或膨胀来精确调整面积
        adjust_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        max_iters = 100  # 防止无限循环

        for i in range(max_iters):
            # 检查面积是否在目标范围的10%以内
            if abs(current_area - target_area) / (target_area + 1e-6) < 0.1:
                break
            
            last_area = current_area
            if current_area > target_area:
                sub_mask = cv2.erode(sub_mask, adjust_kernel)
            else:
                sub_mask = cv2.dilate(sub_mask, adjust_kernel)
                # 限制膨胀在原始轮廓内
                sub_mask = cv2.bitwise_and(sub_mask, mask_single)
            
            current_area = cv2.countNonZero(sub_mask)
            # 如果面积不再改变，则停止
            if current_area == last_area:
                break

        # 边缘平滑
        if cv2.countNonZero(sub_mask) > 0:
            sub_mask_float = sub_mask.astype(np.float32) / 255.0
            smooth_mask = cv2.GaussianBlur(sub_mask_float, (5, 5), 0)
            sub_mask = (smooth_mask > 0.5).astype(np.uint8) * 255
        return sub_mask

    def generate(self):
        overall_total_generated = 0
        patch_size = self.input_resolution

        for triple in self.triples:
            normal_dir, anomaly_img_dir, anomaly_mask_dir, class_name, defect_name = triple

            normal_paths = self._load_image_list(normal_dir)
            anomaly_img_paths = self._load_image_list(anomaly_img_dir)
            anomaly_mask_paths = []
            for img_path in anomaly_img_paths:
                base = os.path.splitext(os.path.basename(img_path))[0]
                mask_path = os.path.join(anomaly_mask_dir, base + '_mask.png')
                if os.path.exists(mask_path):
                    anomaly_mask_paths.append(mask_path)

            if not normal_paths:
                # print(f"[{class_name}/{defect_name}] 警告: 在 '{normal_dir}' 中未找到正常样本图片，跳过此缺陷类型。")  # 注释掉UI不需要的调试信息
                continue
            if not anomaly_img_paths or not anomaly_mask_paths:
                print(f"[{class_name}/{defect_name}] 未找到异常图片或掩码，跳过")
                continue

            # 输出目录结构
            out_img_dir = os.path.join(self.output_root, class_name, 'images', defect_name)
            out_mask_dir = os.path.join(self.output_root, class_name, 'ground_truth', defect_name)
            os.makedirs(out_img_dir, exist_ok=True)
            os.makedirs(out_mask_dir, exist_ok=True)

            # 统计已有编号，防止覆盖，从最大编号+1开始
            existing_imgs = self._load_image_list(out_img_dir)
            start_idx = 0
            if existing_imgs:
                max_num = -1
                for img_path in existing_imgs:
                    try:
                        num = int(os.path.splitext(os.path.basename(img_path))[0])
                        if num > max_num:
                            max_num = num
                    except ValueError:
                        continue # 忽略无法解析为数字的文件名
                start_idx = max_num + 1

            print(f"[{class_name}/{defect_name}] 目录中已有 {start_idx} 个样本，将从该序号开始生成。")
            print(f"[{class_name}/{defect_name}] 开始为每张正常样本生成 {self.samples_per_normal_image} 个合成样本...")
            
            defect_type_total = 0
            pbar = tqdm(normal_paths, desc=f"处理 {class_name}/{defect_name}")
            for normal_path in pbar:
                normal_img_orig = cv2.imread(normal_path)
                normal_img = cv2.cvtColor(normal_img_orig, cv2.COLOR_BGR2RGB)
                h, w = normal_img.shape[:2]
                normal_basename = os.path.splitext(os.path.basename(normal_path))[0]
                pbar.set_postfix_str(f"基底: {normal_basename}")

                for j in range(self.samples_per_normal_image):
                    # 随机选择一个异常图像和掩码作为缺陷源
                    idx = random.randint(0, len(anomaly_img_paths) - 1)
                    anomaly_img_orig = cv2.imread(anomaly_img_paths[idx])
                    anomaly_img = cv2.cvtColor(anomaly_img_orig, cv2.COLOR_BGR2RGB)
                    mask_orig = cv2.imread(anomaly_mask_paths[idx], cv2.IMREAD_GRAYSCALE)

                    contours, _ = cv2.findContours(mask_orig, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                    if not contours:
                        continue

                    cnt = random.choice(contours)
                    x, y, ww, hh = cv2.boundingRect(cnt)
                    if ww < 10 or hh < 10:
                        continue

                    full_mask_roi = mask_orig[y:y + hh, x:x + ww]
                    sub_mask = self.random_submask_within(full_mask_roi, self.min_defect_ratio, self.max_defect_ratio)
                    if cv2.countNonZero(sub_mask) == 0:
                        continue

                    soft_mask = cv2.GaussianBlur(sub_mask.astype(np.float32), (11, 11), 0)
                    soft_mask = soft_mask / 255.0
                    soft_mask = np.clip(soft_mask, 0, 1)

                    defect_patch = anomaly_img[y:y + hh, x:x + ww]
                    
                    if normal_img.shape[:2] != anomaly_img.shape[:2]:
                        normal_img_resized_for_patch = cv2.resize(normal_img, (anomaly_img.shape[1], anomaly_img.shape[0]))
                    else:
                        normal_img_resized_for_patch = normal_img

                    normal_patch = normal_img_resized_for_patch[y:y + hh, x:x + ww]

                    defect_patch_resized = cv2.resize(defect_patch, patch_size)
                    normal_patch_resized = cv2.resize(normal_patch, patch_size)

                    # 获取当前缺陷类型ID
                    defect_key = f"{class_name}_{defect_name}"
                    defect_type_id = self.defect_type_to_id[defect_key]
                    defect_type_tensor = torch.tensor([defect_type_id], device=self.device)

                    normal_tensor = self.transform(normal_patch_resized).unsqueeze(0).to(self.device)
                    with torch.no_grad():
                        fake_patch, _ = self.generator(normal_tensor, defect_type_tensor)
                    fake_patch_np = fake_patch.squeeze(0).cpu().numpy().transpose(1, 2, 0)
                    fake_patch_np = ((fake_patch_np * 0.5 + 0.5) * 255.0).astype(np.uint8)

                    synth_img = normal_img.copy()
                    synth_mask = np.zeros((h, w), dtype=np.uint8)
                    roi = synth_img[y:y + hh, x:x + ww].astype(np.float32)
                    fake_patch_np = cv2.resize(fake_patch_np, (ww, hh))

                    gray_fake = cv2.cvtColor(fake_patch_np, cv2.COLOR_RGB2GRAY)
                    edges = cv2.Canny(gray_fake, 50, 150) / 255.0
                    edges = cv2.dilate(edges, np.ones((3, 3), np.uint8))
                    edges = cv2.GaussianBlur(edges, (5, 5), 0)
                    edge_weight = 1 - edges[..., None] * 0.3
                    blend_mask = soft_mask[..., None] * edge_weight
                    fused = roi * (1 - blend_mask) + fake_patch_np * blend_mask
                    synth_img[y:y + hh, x:x + ww] = np.clip(fused, 0, 255).astype(np.uint8)
                    synth_mask[y:y + hh, x:x + ww] = (soft_mask * 255).astype(np.uint8)

                    # 更新命名方式为纯序号
                    current_idx = start_idx + defect_type_total
                    out_name = f"{current_idx:04d}.png"
                    mask_name = f"{current_idx:04d}_mask.png"
                    cv2.imwrite(os.path.join(out_img_dir, out_name), cv2.cvtColor(synth_img, cv2.COLOR_RGB2BGR))
                    cv2.imwrite(os.path.join(out_mask_dir, mask_name), synth_mask)
                    defect_type_total += 1
            
            pbar.close()
            print(f"[{class_name}/{defect_name}] 共生成 {defect_type_total} 个合成缺陷样本。")
            overall_total_generated += defect_type_total
        
        print(f"总共生成 {overall_total_generated} 个合成缺陷样本。")
        return overall_total_generated

    def generate_specific_defect_type(self, target_defect_key, num_samples=5):
        """生成指定缺陷类型的样本"""
        if target_defect_key not in self.defect_type_to_id:
            print(f"错误: 缺陷类型 '{target_defect_key}' 不存在")
            return 0

        defect_type_id = self.defect_type_to_id[target_defect_key]
        defect_type_tensor = torch.tensor([defect_type_id], device=self.device)

        # 找到对应的triple
        target_triple = None
        for triple in self.triples:
            _, _, _, class_name, defect_name = triple
            if f"{class_name}_{defect_name}" == target_defect_key:
                target_triple = triple
                break

        if target_triple is None:
            print(f"错误: 找不到缺陷类型 '{target_defect_key}' 对应的数据")
            return 0

        normal_dir, anomaly_img_dir, anomaly_mask_dir, class_name, defect_name = target_triple

        # 创建输出目录
        output_dir = os.path.join(self.output_root, f"conditional_{class_name}_{defect_name}")
        os.makedirs(output_dir, exist_ok=True)

        normal_paths = self._load_image_list(normal_dir)
        if not normal_paths:
            print(f"警告: 在 {normal_dir} 中未找到正常图像")
            return 0

        patch_size = self.input_resolution
        generated_count = 0

        for i in range(num_samples):
            # 随机选择一张正常图像
            normal_path = random.choice(normal_paths)
            normal_img_orig = cv2.imread(normal_path)
            normal_img = cv2.cvtColor(normal_img_orig, cv2.COLOR_BGR2RGB)

            # 随机裁剪一个patch
            h, w = normal_img.shape[:2]
            if h >= patch_size[0] and w >= patch_size[1]:
                y = random.randint(0, h - patch_size[0])
                x = random.randint(0, w - patch_size[1])
                normal_patch = normal_img[y:y + patch_size[0], x:x + patch_size[1]]
            else:
                normal_patch = cv2.resize(normal_img, patch_size)

            # 生成缺陷样本
            normal_tensor = self.transform(normal_patch).unsqueeze(0).to(self.device)
            with torch.no_grad():
                fake_patch, fake_mask = self.generator(normal_tensor, defect_type_tensor)

            # 转换为numpy
            fake_patch_np = fake_patch.squeeze(0).cpu().numpy().transpose(1, 2, 0)
            fake_patch_np = ((fake_patch_np * 0.5 + 0.5) * 255.0).astype(np.uint8)

            fake_mask_np = torch.sigmoid(fake_mask).squeeze(0).squeeze(0).cpu().numpy()
            fake_mask_np = (fake_mask_np * 255).astype(np.uint8)

            # 保存结果
            normal_basename = os.path.splitext(os.path.basename(normal_path))[0]
            output_img_path = os.path.join(output_dir, f"{normal_basename}_conditional_{i:03d}.jpg")
            output_mask_path = os.path.join(output_dir, f"{normal_basename}_conditional_{i:03d}_mask.png")

            cv2.imwrite(output_img_path, cv2.cvtColor(fake_patch_np, cv2.COLOR_RGB2BGR))
            cv2.imwrite(output_mask_path, fake_mask_np)
            generated_count += 1

        print(f"为缺陷类型 '{target_defect_key}' 生成了 {generated_count} 个条件样本")
        return generated_count


if __name__ == "__main__":
    # ------------------------------------------------------------
    # 配置参数
    # ------------------------------------------------------------

    MODE = "train"  # 可选: "train", "generate"
    INPUT_MODE = "auto"  # 可选: "auto" 或 "manual"

    # ========== 自动模式参数 ==========
    # 只在 INPUT_MODE == 'auto' 时填写
    INPUT_PATH = r"D:\YOLO\datasets\MVTec_AD\bottle"  # 根目录或产品类别目录

    # ========== 手动三路径模式参数 ==========
    # 只在 INPUT_MODE == 'manual' 时填写
    NORMAL_DIR = r"D:\YOLO\datasets\MVTec_AD\bottle\test\good"  # 如 r"D:\YOLO\datasets\MVTec_AD\bottle\test\good"
    ANOMALY_IMG_DIR = r"D:\YOLO\datasets\MVTec_AD\bottle\test\broken_large"  # 如 r"D:\YOLO\datasets\MVTec_AD\bottle\test\broken_large"
    ANOMALY_MASK_DIR = r"D:\YOLO\datasets\MVTec_AD\bottle\ground_truth\broken_large"  # 如 r"D:\YOLO\datasets\MVTec_AD\bottle\ground_truth\broken_large"

    # ========== 通用参数 ==========
    OUTPUT_ROOT_DIR = r"D:\YOLO\output18"
    MODEL_DIR = os.path.join(OUTPUT_ROOT_DIR, "models")
    MODEL_PATH = os.path.join(MODEL_DIR, "generator.pth")
    EPOCHS = 200 # 进一步增加训练轮数，确保充分对抗训练
    BATCH_SIZE = 4  # 进一步减小batch_size提高稳定性
    # 简化的学习率设置
    G_LEARNING_RATE = 0.0002   # 标准生成器学习率
    D_LEARNING_RATE = 0.0002   # 标准判别器学习率
    SAMPLES_PER_NORMAL_IMAGE = 1  # 为每张正常样本生成的缺陷图数量
    PATCH_SIZE = (128, 128)  # 平衡显存和质量的patch_size
    VERBOSE_DEBUG = False  # 控制是否显示详细调试信息（UI界面建议设为False）
    # 缺陷采样面积比例（用户可自定义）
    MIN_DEFECT_RATIO = 0.5  # 最小缺陷面积比例
    MAX_DEFECT_RATIO = 0.8  # 最大缺陷面积比例

    # ------------------------------------------------------------
    # 自动采集triples
    # ------------------------------------------------------------
    triples = []
    if INPUT_MODE == "manual":
        # 三路径输入
        if not (NORMAL_DIR and ANOMALY_IMG_DIR and ANOMALY_MASK_DIR):
            print("manual模式下，必须填写NORMAL_DIR、ANOMALY_IMG_DIR、ANOMALY_MASK_DIR")
            exit(1)
        if not os.path.isdir(NORMAL_DIR):
            print(f"正常样本路径不存在: {NORMAL_DIR}")
            exit(1)
        if not os.path.isdir(ANOMALY_IMG_DIR):
            print(f"异常样本路径不存在: {ANOMALY_IMG_DIR}")
            exit(1)
        if not os.path.isdir(ANOMALY_MASK_DIR):
            print(f"掩码路径不存在: {ANOMALY_MASK_DIR}")
            exit(1)
        class_name = os.path.basename(os.path.dirname(NORMAL_DIR))
        defect_name = os.path.basename(ANOMALY_IMG_DIR)
        triples = [(NORMAL_DIR, ANOMALY_IMG_DIR, ANOMALY_MASK_DIR, class_name, defect_name)]
    elif INPUT_MODE == "auto":
        # 根目录或产品类别目录
        if not os.path.isdir(INPUT_PATH):
            print(f"输入路径不存在: {INPUT_PATH}")
            exit(1)
        collector = AutoMVTecDatasetCollector(INPUT_PATH)
        triples = collector.get_triples()
        if len(triples) == 0:
            if os.path.isdir(os.path.join(INPUT_PATH, 'images')) and os.path.isdir(
                    os.path.join(INPUT_PATH, 'ground_truth')):
                print(f"该产品类别目录下未找到有效的缺陷类型，请检查: {INPUT_PATH}")
            else:
                print(f"未找到有效的样本三元组，请检查输入路径: {INPUT_PATH}")
            exit(1)
    else:
        print("INPUT_MODE 仅支持 'auto' 或 'manual'")
        exit(1)

    # ------------------------------------------------------------
    # 统一主入口
    # ------------------------------------------------------------
    if MODE == "train":
        print(f"=== 训练模式 [gan] ===")
        os.makedirs(OUTPUT_ROOT_DIR, exist_ok=True)
        os.makedirs(MODEL_DIR, exist_ok=True)

        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"使用设备: {device}")

        # 创建数据集以获取缺陷类型数量
        dataset = MultiDefectPatchDataset(triples, patch_size=PATCH_SIZE, augment=True)
        num_defect_types = len(dataset.defect_type_to_id)
        print(f"检测到 {num_defect_types} 种缺陷类型: {list(dataset.defect_type_to_id.keys())}")

        # 初始化模型
        generator = UniversalDefectGenerator(num_defect_types=num_defect_types).to(device)
        discriminator = UniversalDefectDiscriminator().to(device)

        # 优化模型初始化 - 让生成器有更好的起点
        def init_weights(m):
            if isinstance(m, nn.Conv2d) or isinstance(m, nn.ConvTranspose2d):
                # 使用Xavier初始化，更适合生成器
                nn.init.xavier_normal_(m.weight, gain=0.02)  # 很小的gain
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.InstanceNorm2d):
                if m.weight is not None:
                    nn.init.constant_(m.weight, 1)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

        # 只对生成器使用特殊初始化，判别器保持默认
        generator.apply(init_weights)



        # 初始化损失函数
        criterion = UniversalDefectLoss().to(device)

        # 优化的优化器配置 - 平衡对抗学习
        g_optimizer = optim.Adam(generator.parameters(), lr=0.0002, betas=(0.5, 0.999))  # 标准GAN学习率
        d_optimizer = optim.Adam(discriminator.parameters(), lr=0.0002, betas=(0.5, 0.999))  # 匹配生成器学习率

        # 改进的学习率调度器 - 使用余弦退火
        g_scheduler = optim.lr_scheduler.CosineAnnealingLR(g_optimizer, T_max=EPOCHS//2, eta_min=1e-6)
        d_scheduler = optim.lr_scheduler.CosineAnnealingLR(d_optimizer, T_max=EPOCHS//2, eta_min=1e-6)

        # 数据加载（使用已创建的数据集）
        if len(dataset) == 0:
            print("错误: 没有足够的训练样本。请确保提供了缺陷样本和对应的掩码。")
            exit(1)
        print(f"找到 {len(dataset)} 对训练样本")
        print(f"--- 数据集信息: 最终用于训练的样本总数为: {len(dataset)} ---")
        dataloader = DataLoader(
            dataset,
            batch_size=BATCH_SIZE,
            shuffle=True,
            num_workers=0,  # 减少worker数量节省内存
            pin_memory=False  # 关闭pin_memory节省内存
        )

        # ========= 主对抗训练 =========
        print("--- 开始主对抗训练 ---")
        # 确保模型回到正确的模式
        generator.train()
        discriminator.train()

        # 调用修改后的 train_defect_gan 函数 - 使用平衡对抗训练策略
        train_defect_gan(
            dataloader,
            generator, discriminator,
            g_optimizer, d_optimizer,
            criterion,
            g_scheduler, d_scheduler,  # 传递调度器
            epochs=EPOCHS,  # 主训练的Epochs
            save_dir=MODEL_DIR,
            device=device,
            d_train_ratio=1,  # 判别器训练频率
            g_train_ratio=1   # 生成器训练频率，通过内部动态调整实现平衡
        )

        # 保存最终模型
        torch.save(generator.state_dict(), MODEL_PATH)
        torch.save(discriminator.state_dict(), f"{MODEL_DIR}/discriminator_final.pth")
        print(f"模型已保存到 {MODEL_PATH}")
    elif MODE == "generate":
        print(f"=== 生成模式 [gan] ===")
        os.makedirs(OUTPUT_ROOT_DIR, exist_ok=True)

        # 优化：在生成模式下检查模型文件是否存在
        if not os.path.exists(MODEL_PATH):
            print(f"错误: 模型文件不存在: {MODEL_PATH}")
            print("生成模式需要训练好的模型，请先运行训练模式")
            exit(1)

        config = {
            'triples': triples,
            'output_root': OUTPUT_ROOT_DIR,
            'model_path': MODEL_PATH,
            'samples_per_normal_image': SAMPLES_PER_NORMAL_IMAGE,
            'input_resolution': PATCH_SIZE,
            'min_defect_ratio': MIN_DEFECT_RATIO,
            'max_defect_ratio': MAX_DEFECT_RATIO
        }
        print("开始生成合成缺陷样本...")
        synthesizer = DefectGANSynthesizer(config)

        # 支持指定缺陷类型生成
        print(f"可用的缺陷类型: {list(synthesizer.defect_type_to_id.keys())}")
        print("生成所有类型的缺陷样本...")
        total = synthesizer.generate()
        print(f"完成! 已生成 {total} 个合成缺陷样本")

        # 额外生成指定类型的样本（演示用）
        print("\n=== 条件生成演示 ===")
        for defect_key in list(synthesizer.defect_type_to_id.keys())[:3]:  # 只演示前3种
            print(f"生成指定类型 '{defect_key}' 的样本...")
            synthesizer.generate_specific_defect_type(defect_key, num_samples=2)
        print("条件生成演示完成!")
    else:
        print(f"错误: 未知MODE '{MODE}'，请设置为 'train' 或 'generate'")





---
description: Deep dive into Ultralytics callbacks. Learn how to use the _log_plots, on_fit_epoch_end, and on_train_end functions effectively.
keywords: Ultralytics, callbacks, _log_plots, on_fit_epoch_end, on_train_end
---

# Reference for `ultralytics/utils/callbacks/wb.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/wb.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/wb.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/callbacks/wb.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.callbacks.wb._custom_table

<br><br>

## ::: ultralytics.utils.callbacks.wb._plot_curve

<br><br>

## ::: ultralytics.utils.callbacks.wb._log_plots

<br><br>

## ::: ultralytics.utils.callbacks.wb.on_pretrain_routine_start

<br><br>

## ::: ultralytics.utils.callbacks.wb.on_fit_epoch_end

<br><br>

## ::: ultralytics.utils.callbacks.wb.on_train_epoch_end

<br><br>

## ::: ultralytics.utils.callbacks.wb.on_train_end

<br><br>

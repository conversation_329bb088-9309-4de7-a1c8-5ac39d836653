---
description: Uncover a detailed guide to Ultralytics data utilities. Learn functions from img2label_paths to autosplit, all boosting your YOLO model’s efficiency.
keywords: Ultralytics, data utils, YOLO, img2label_paths, exif_size, polygon2mask, polygons2masks_overlap, check_cls_dataset, delete_dsstore, autosplit
---

# Reference for `ultralytics/data/utils.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/utils.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/utils.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/data/utils.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.data.utils.HUBDatasetStats

<br><br>

## ::: ultralytics.data.utils.img2label_paths

<br><br>

## ::: ultralytics.data.utils.get_hash

<br><br>

## ::: ultralytics.data.utils.exif_size

<br><br>

## ::: ultralytics.data.utils.verify_image

<br><br>

## ::: ultralytics.data.utils.verify_image_label

<br><br>

## ::: ultralytics.data.utils.polygon2mask

<br><br>

## ::: ultralytics.data.utils.polygons2masks

<br><br>

## ::: ultralytics.data.utils.polygons2masks_overlap

<br><br>

## ::: ultralytics.data.utils.find_dataset_yaml

<br><br>

## ::: ultralytics.data.utils.check_det_dataset

<br><br>

## ::: ultralytics.data.utils.check_cls_dataset

<br><br>

## ::: ultralytics.data.utils.compress_one_image

<br><br>

## ::: ultralytics.data.utils.autosplit

<br><br>

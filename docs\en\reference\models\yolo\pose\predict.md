---
description: Discover how to use PosePredictor in the Ultralytics YOLO model. Includes detailed guides, code examples, and explanations.
keywords: Ultralytics, YOLO, PosePredictor, machine learning, AI, predictive models
---

# Reference for `ultralytics/models/yolo/pose/predict.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/pose/predict.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/pose/predict.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/pose/predict.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.yolo.pose.predict.PosePredictor

<br><br>

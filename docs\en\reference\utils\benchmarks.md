---
description: Discover how to profile your models using Ultralytics utilities. Enhance performance, optimize your benchmarks, and learn best practices.
keywords: Ultralytics, ProfileModels, benchmarks, model profiling, performance optimization
---

# Reference for `ultralytics/utils/benchmarks.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/benchmarks.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/benchmarks.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/benchmarks.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.benchmarks.ProfileModels

<br><br>

## ::: ultralytics.utils.benchmarks.benchmark

<br><br>
